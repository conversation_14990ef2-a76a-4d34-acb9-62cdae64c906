<?php

//app/Services/OrderslipService.php
namespace App\Services;

use App\Models\OrderSlipHeader;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Exception;
use App\Services\GetCurrentTransactionService;
use App\Services\RemoveCurrentTransactionService;
use App\Services\CartInformationService;

class OrderslipService
{
    public function __construct(
        protected GetCurrentTransactionService $getCurrentTransaction,
        protected RemoveCurrentTransactionService $removeCurrentTransaction,
        protected CartInformationService $cartService
    ) {}

    public function list(Request $request)
    {
        return OrderSlipHeader::with('terminal')
            ->when($request->branch_id, fn($q) => $q->where('BRANCHID', $request->branch_id))
            ->when($request->outlet_id, fn($q) => $q->where('OUTLETID', $request->outlet_id))
            ->when($request->boolean('hide_completed'), fn($q) => $q->whereNull('QDATE'))
            ->when($request->boolean('show_completed'), fn($q) => $q->whereNotNull('QDATE'))
            ->orderBy('OSDATE', 'desc')
            ->paginate($request->limit ?? 10);
    }

    public function create(Request $request)
    {
        try {
            DB::beginTransaction();

            // Check current transaction
            $result = $this->getCurrentTransaction->handle($request->branch_id, $request->user_id);

            if ($result['success']) {
                $this->removeCurrentTransaction->handle($request->branch_id, $request->user_id);
            }

            // Reset other customer display flags
            OrderSlipHeader::where('DEVICENO', $request->device_id)
                ->where('BRANCHID', $request->branch_id)
                ->where('CUSTOMERDISPLAY', 1)
                ->update(['CUSTOMERDISPLAY' => 0]);

            // Generate ID + OS number
            $orderslip_new_id = OrderSlipHeader::getNewId($request->branch_id, $request->outlet_id, $request->device_id);
            $osnumber = $this->osNumberGenerator($request->branch_id, $request->device_id, now(), $orderslip_new_id);

            // Build payload
            $payload = $this->buildPayload($request, $orderslip_new_id, $osnumber);

            OrderSlipHeader::create($payload);

            DB::commit();
            return response()->json([
                'message' => 'Ok',
                'osnumber' => $osnumber
            ]);
        } catch (Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => 'SERVER ERROR',
                'system' => $e->getMessage()
            ], 500);
        }
    }



    
    public function show(string $orderslip_code)
    {
        try {
            $cart = $this->cartService->handle($orderslip_code);

            return response()->json([
                'message' => 'Ok',
                'data' => $cart['data']
            ]);
        } catch (Exception $e) {
            return response()->json([
                'message' => 'SERVER ERROR',
                'system' => $e->getMessage()
            ], 500);
        }
    }

    private function buildPayload(Request $request, int $orderslip_new_id, string $osnumber): array
    {
        $payload = [
            'ORDERSLIPNO' => $orderslip_new_id,
            'BRANCHID' => $request->branch_id,
            'OUTLETID' => $request->outlet_id,
            'DEVICENO' => $request->device_id,
            'STATUS' => 'X',
            'DISPLAYMONITOR' => 2,
            'ENCODEDBY' => $request->user_id,
            'PREPAREDBY' => $request->user_name,
            'CCENAME' => $request->user_name,
            'TRANSACTTYPEID' => 1,
            'OSDATE' => now(),
            'ORIGINALINVOICEDATE' => $this->getClarionDate(now()),
            'ENCODEDDATE' => now(),
            'OSNUMBER' => $osnumber,
            'USER_CURRENT_TRANSACTION' => $request->user_id,
            'BUSDATE' => $this->getClarionDate(now()),
            'PAID' => 0,
            'ACCOUNTTYPE' => 0,
        ];

        if (config('settings.app_type') == 'restaurant_ambulant') {
            $payload['OSTYPE'] = config('settings.default_order_type');
            $payload['SERVICE_CHARGE_AMT'] = 0;

            $payload['SERVICE_CHARGE_PERCENTAGE'] =
                ($payload['OSTYPE'] == 1) // Dine-in
                ? config('settings.service_charge_percentage')
                : 0;
        }

        return $payload;
    }

    public function getClarionDate(Carbon $date): int
    {
        $start_date = '1801-01-01';
        $start_from = Carbon::parse($start_date);
        return $date->diffInDays($start_from) + 4;
    }

    public function osNumberGenerator($branch_id, $device_id, Carbon $date, $orderslip_id): string
    {
        $device = str_pad($device_id, 2, "0", STR_PAD_LEFT);
        $orderslip = str_pad($orderslip_id, 5, "0", STR_PAD_LEFT);

        return "{$branch_id}{$device}-{$date->format('ymd')}{$orderslip}";
    }

    public function computeDuration(Carbon $start, $end): string
    {
        if ($end == null) {
            return '--:--:--';
        }

        $end = Carbon::parse($end);
        $hours = $start->diffInHours($end);
        $minutes = $start->diffInMinutes($end) % 60;
        $seconds = $start->diffInSeconds($end) % 60;

        return "{$hours}:{$minutes}:{$seconds}";
    }
}
