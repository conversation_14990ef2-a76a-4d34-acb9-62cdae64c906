<?php

namespace App\Http\Services;

use App\Models\OrderSlipHeader;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Exception;
use App\Http\Services\GetCurrentTransactionService;
use App\Http\Services\RemoveCurrentTransactionService;
use App\Http\Services\CartInformationService;

class OrderslipService
{
    public function __construct(
        protected GetCurrentTransactionService $getCurrentTransaction,
        protected RemoveCurrentTransactionService $removeCurrentTransaction,
        protected CartInformationService $cartService
    ) {}

    public function list(Request $request)
    {
        return OrderSlipHeader::with('terminal')
            ->when($request->branch_id, fn($q) => $q->where('BRANCHID', $request->branch_id))
            ->when($request->outlet_id, fn($q) => $q->where('OUTLETID', $request->outlet_id))
            ->when($request->boolean('hide_completed'), fn($q) => $q->whereNull('QDATE'))
            ->when($request->boolean('show_completed'), fn($q) => $q->whereNotNull('QDATE'))
            ->orderBy('OSDATE', 'desc')
            ->paginate($request->limit ?? 10);
    }

    public function create(Request $request)
    {
        try {
            DB::beginTransaction();

            // Check current transaction
            $result = $this->getCurrentTransaction->handle($request->branch_id, $request->user_id);

            if ($result['success']) {
                $this->removeCurrentTransaction->handle($request->branch_id, $request->user_id);
            }

            // Reset other customer display flags
            OrderSlipHeader::where('DEVICENO', $request->device_id)
                ->where('BRANCHID', $request->branch_id)
                ->where('CUSTOMERDISPLAY', 1)
                ->update(['CUSTOMERDISPLAY' => 0]);

            // Generate ID + OS number
            $orderslip_new_id = OrderSlipHeader::getNewId($request->branch_id, $request->outlet_id, $request->device_id);
            $osnumber = $this->osNumberGenerator($request->branch_id, $request->device_id, now(), $orderslip_new_id);

            // Build payload
            $payload = $this->buildPayload($request, $orderslip_new_id, $osnumber);

            $orderSlip = OrderSlipHeader::create($payload);

            DB::commit();
            return [
                'success' => true,
                'message' => 'Order slip created successfully',
                'data' => [
                    'orderslip_id' => $orderslip_new_id,
                    'osnumber' => $osnumber,
                    'status' => 'active',
                    'created_at' => $orderSlip->OSDATE
                ]
            ];
        } catch (Exception $e) {
            DB::rollBack();
            return [
                'success' => false,
                'message' => 'Failed to create order slip',
                'error' => $e->getMessage()
            ];
        }
    }

    public function getUserActiveOrderSlip($userId, $branchId = null, $outletId = null)
    {
        try {
            $query = OrderSlipHeader::where('USER_CURRENT_TRANSACTION', $userId)
                ->whereNull('QDATE') // Not completed
                ->where('PAID', 0); // Not paid

            if ($branchId) {
                $query->where('BRANCHID', $branchId);
            }

            if ($outletId) {
                $query->where('OUTLETID', $outletId);
            }

            $activeSlip = $query->first();

            return [
                'success' => true,
                'data' => $activeSlip ? [
                    'orderslip_id' => $activeSlip->ORDERSLIPNO,
                    'osnumber' => $activeSlip->OSNUMBER,
                    'status' => 'active',
                    'created_at' => $activeSlip->OSDATE,
                    'branch_id' => $activeSlip->BRANCHID,
                    'outlet_id' => $activeSlip->OUTLETID,
                    'device_id' => $activeSlip->DEVICENO
                ] : null
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to get active order slip',
                'error' => $e->getMessage()
            ];
        }
    }

    public function getUserOrderSlips($userId, $branchId = null, $limit = 10)
    {
        try {
            $query = OrderSlipHeader::where('ENCODEDBY', $userId)
                ->with('terminal')
                ->orderBy('OSDATE', 'desc');

            if ($branchId) {
                $query->where('BRANCHID', $branchId);
            }

            $orderSlips = $query->limit($limit)->get();

            $formattedSlips = $orderSlips->map(function ($slip) use ($userId) {
                return [
                    'orderslip_id' => $slip->ORDERSLIPNO,
                    'osnumber' => $slip->OSNUMBER,
                    'status' => $this->getOrderSlipStatus($slip),
                    'created_at' => $slip->OSDATE,
                    'completed_at' => $slip->QDATE,
                    'total_amount' => $slip->TOTALAMOUNT ?? 0,
                    'is_active' => $slip->USER_CURRENT_TRANSACTION == $userId && !$slip->QDATE && !$slip->PAID,
                    'branch_id' => $slip->BRANCHID,
                    'outlet_id' => $slip->OUTLETID,
                    'device_id' => $slip->DEVICENO
                ];
            });

            return [
                'success' => true,
                'data' => $formattedSlips
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to get user order slips',
                'error' => $e->getMessage()
            ];
        }
    }

    public function attachOrderSlip($osnumber, $userId)
    {
        try {
            DB::beginTransaction();

            // Detach any current active order slip for this user
            OrderSlipHeader::where('USER_CURRENT_TRANSACTION', $userId)
                ->whereNull('QDATE')
                ->where('PAID', 0)
                ->update(['USER_CURRENT_TRANSACTION' => null]);

            // Attach the specified order slip
            $orderSlip = OrderSlipHeader::where('OSNUMBER', $osnumber)->first();

            if (!$orderSlip) {
                DB::rollBack();
                return [
                    'success' => false,
                    'message' => 'Order slip not found'
                ];
            }

            if ($orderSlip->QDATE || $orderSlip->PAID) {
                DB::rollBack();
                return [
                    'success' => false,
                    'message' => 'Cannot attach completed or paid order slip'
                ];
            }

            $orderSlip->update(['USER_CURRENT_TRANSACTION' => $userId]);

            DB::commit();
            return [
                'success' => true,
                'message' => 'Order slip attached successfully',
                'data' => [
                    'orderslip_id' => $orderSlip->ORDERSLIPNO,
                    'osnumber' => $orderSlip->OSNUMBER,
                    'status' => 'active'
                ]
            ];
        } catch (Exception $e) {
            DB::rollBack();
            return [
                'success' => false,
                'message' => 'Failed to attach order slip',
                'error' => $e->getMessage()
            ];
        }
    }

    public function detachOrderSlip($osnumber, $userId)
    {
        try {
            $orderSlip = OrderSlipHeader::where('OSNUMBER', $osnumber)
                ->where('USER_CURRENT_TRANSACTION', $userId)
                ->first();

            if (!$orderSlip) {
                return [
                    'success' => false,
                    'message' => 'Order slip not found or not attached to user'
                ];
            }

            $orderSlip->update(['USER_CURRENT_TRANSACTION' => null]);

            return [
                'success' => true,
                'message' => 'Order slip detached successfully'
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to detach order slip',
                'error' => $e->getMessage()
            ];
        }
    }

    private function getOrderSlipStatus($orderSlip)
    {
        if ($orderSlip->QDATE) {
            return 'completed';
        }
        if ($orderSlip->PAID) {
            return 'paid';
        }
        return 'pending';
    }



    
    public function show(string $orderslip_code)
    {
        try {
            $cart = $this->cartService->handle($orderslip_code);

            return response()->json([
                'message' => 'Ok',
                'data' => $cart['data']
            ]);
        } catch (Exception $e) {
            return response()->json([
                'message' => 'SERVER ERROR',
                'system' => $e->getMessage()
            ], 500);
        }
    }

    private function buildPayload(Request $request, int $orderslip_new_id, string $osnumber): array
    {
        $payload = [
            'ORDERSLIPNO' => $orderslip_new_id,
            'BRANCHID' => $request->branch_id,
            'OUTLETID' => $request->outlet_id,
            'DEVICENO' => $request->device_id,
            'STATUS' => 'X',
            'DISPLAYMONITOR' => 2,
            'ENCODEDBY' => $request->user_id,
            'PREPAREDBY' => $request->user_name,
            'CCENAME' => $request->user_name,
            'TRANSACTTYPEID' => 1,
            'OSDATE' => now(),
            'ORIGINALINVOICEDATE' => $this->getClarionDate(now()),
            'ENCODEDDATE' => now(),
            'OSNUMBER' => $osnumber,
            'USER_CURRENT_TRANSACTION' => $request->user_id,
            'BUSDATE' => $this->getClarionDate(now()),
            'PAID' => 0,
            'ACCOUNTTYPE' => 0,
        ];

        if (config('settings.app_type') == 'restaurant_ambulant') {
            $payload['OSTYPE'] = config('settings.default_order_type');
            $payload['SERVICE_CHARGE_AMT'] = 0;

            $payload['SERVICE_CHARGE_PERCENTAGE'] =
                ($payload['OSTYPE'] == 1) // Dine-in
                ? config('settings.service_charge_percentage')
                : 0;
        }

        return $payload;
    }

    public function getClarionDate(Carbon $date): int
    {
        $start_date = '1801-01-01';
        $start_from = Carbon::parse($start_date);
        return $date->diffInDays($start_from) + 4;
    }

    public function osNumberGenerator($branch_id, $device_id, Carbon $date, $orderslip_id): string
    {
        $device = str_pad($device_id, 2, "0", STR_PAD_LEFT);
        $orderslip = str_pad($orderslip_id, 5, "0", STR_PAD_LEFT);

        return "{$branch_id}{$device}-{$date->format('ymd')}{$orderslip}";
    }

    public function computeDuration(Carbon $start, $end): string
    {
        if ($end == null) {
            return '--:--:--';
        }

        $end = Carbon::parse($end);
        $hours = $start->diffInHours($end);
        $minutes = $start->diffInMinutes($end) % 60;
        $seconds = $start->diffInSeconds($end) % 60;

        return "{$hours}:{$minutes}:{$seconds}";
    }
}
