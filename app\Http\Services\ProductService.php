<?php

namespace App\Http\Services;

use App\Http\Resources\ProductResource;
use App\Models\Product;
use App\Models\Department;
use Illuminate\Http\Request;
use Exception;

class ProductService
{
    /**
     * Get all products with optional filtering
     */
    public function getProducts(Request $request)
    {
        try {
            $query = Product::query();

            // Filter by department
            if ($request->has('department_id') && $request->department_id !== '') {
                $query->where('Department_ID', $request->department_id);
            }

            // Search by product description
            if ($request->has('search') && $request->search !== '') {
                $query->where('Product_Desc', 'LIKE', '%' . $request->search . '%');
            }

            // Filter by availability
            if ($request->boolean('available_only')) {
                $query->active(); // Uses the scope from Product model
            }

            // Filter by group meal
            if ($request->has('is_group_meal')) {
                $query->where('isGroupMeal', $request->boolean('is_group_meal'));
            }

            // Pagination
            $perPage = $request->get('per_page', 50);
            $products = $query->paginate($perPage);

            return [
                'success' => true,
                'data' => ProductResource::collection($products->items()),
                'pagination' => [
                    'current_page' => $products->currentPage(),
                    'last_page' => $products->lastPage(),
                    'per_page' => $products->perPage(),
                    'total' => $products->total(),
                    'from' => $products->firstItem(),
                    'to' => $products->lastItem()
                ]
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to fetch products',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get single product by ID
     */
    public function getProduct($productId)
    {
        try {
            $product = Product::where('Product_ID', $productId)->first();

            if (!$product) {
                return [
                    'success' => false,
                    'message' => 'Product not found'
                ];
            }

            return [
                'success' => true,
                'data' => new ProductResource($product)
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to fetch product',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get products by category/department
     */
    public function getProductsByDepartment($departmentId)
    {
        try {
            $department = Department::find($departmentId);

            if (!$department) {
                return [
                    'success' => false,
                    'message' => 'Department not found'
                ];
            }

            $products = Product::where('Department_ID', $departmentId)
                ->active()
                ->get();

            return [
                'success' => true,
                'data' => [
                    'department' => [
                        'id' => $department->Department_ID,
                        'name' => $department->Department_Desc ?? 'Unknown Department'
                    ],
                    'products' => ProductResource::collection($products)
                ]
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to fetch products by department',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Search products by quick code
     */
    public function searchByQuickCode($quickCode)
    {
        try {
            $product = Product::where('Product_Quick_Code', $quickCode)
                ->active()
                ->first();

            if (!$product) {
                return [
                    'success' => false,
                    'message' => 'Product not found with quick code: ' . $quickCode
                ];
            }

            return [
                'success' => true,
                'data' => new ProductResource($product)
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to search product by quick code',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get featured/popular products
     */
    public function getFeaturedProducts($limit = 10)
    {
        try {
            // For now, just return active products ordered by price
            // In the future, this could be based on sales data or manual curation
            $products = Product::active()
                ->orderBy('Product_Price', 'desc')
                ->limit($limit)
                ->get();

            return [
                'success' => true,
                'data' => ProductResource::collection($products)
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to fetch featured products',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get all departments with product counts
     */
    public function getDepartmentsWithProductCounts()
    {
        try {
            $departments = Department::withCount(['products' => function ($query) {
                $query->active();
            }])->get();

            $formattedDepartments = $departments->map(function ($department) {
                return [
                    'id' => $department->Department_ID,
                    'name' => $department->Department_Desc ?? 'Unknown Department',
                    'product_count' => $department->products_count
                ];
            });

            return [
                'success' => true,
                'data' => $formattedDepartments
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to fetch departments',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Check product availability
     */
    public function checkAvailability($productId)
    {
        try {
            $product = Product::where('Product_ID', $productId)->first();

            if (!$product) {
                return [
                    'success' => false,
                    'message' => 'Product not found'
                ];
            }

            $isAvailable = !$product->Delisted;

            return [
                'success' => true,
                'data' => [
                    'product_id' => $product->Product_ID,
                    'available' => $isAvailable,
                    'delisted' => (bool) $product->Delisted,
                    'price' => $product->Product_Price
                ]
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to check product availability',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get product recommendations based on current cart or popular items
     */
    public function getRecommendations($currentProductIds = [], $limit = 5)
    {
        try {
            $query = Product::active();

            // Exclude products already in cart
            if (!empty($currentProductIds)) {
                $query->whereNotIn('Product_ID', $currentProductIds);
            }

            // For now, just return random products
            // In the future, this could be based on purchase history, similar products, etc.
            $products = $query->inRandomOrder()
                ->limit($limit)
                ->get();

            return [
                'success' => true,
                'data' => ProductResource::collection($products)
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to get product recommendations',
                'error' => $e->getMessage()
            ];
        }
    }
}
