<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Auth\Access\AuthorizationException;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

class ApiErrorHandler
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function handle(Request $request, Closure $next): Response
    {
        try {
            return $next($request);
        } catch (Throwable $exception) {
            return $this->handleException($request, $exception);
        }
    }

    /**
     * Handle the exception and return appropriate JSON response.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Throwable  $exception
     * @return \Illuminate\Http\JsonResponse
     */
    protected function handleException(Request $request, Throwable $exception): JsonResponse
    {
        // Log the exception
        \Log::error('API Exception: ' . $exception->getMessage(), [
            'exception' => $exception,
            'request' => [
                'url' => $request->fullUrl(),
                'method' => $request->method(),
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
                'user_id' => auth()->id(),
            ]
        ]);

        // Handle specific exception types
        if ($exception instanceof ValidationException) {
            return $this->handleValidationException($exception);
        }

        if ($exception instanceof ModelNotFoundException) {
            return $this->handleModelNotFoundException($exception);
        }

        if ($exception instanceof AuthenticationException) {
            return $this->handleAuthenticationException($exception);
        }

        if ($exception instanceof AuthorizationException) {
            return $this->handleAuthorizationException($exception);
        }

        // Handle HTTP exceptions
        if (method_exists($exception, 'getStatusCode')) {
            return $this->handleHttpException($exception);
        }

        // Handle general exceptions
        return $this->handleGeneralException($exception);
    }

    /**
     * Handle validation exceptions.
     *
     * @param  \Illuminate\Validation\ValidationException  $exception
     * @return \Illuminate\Http\JsonResponse
     */
    protected function handleValidationException(ValidationException $exception): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => 'Validation failed',
            'errors' => $exception->errors(),
            'error_code' => 'VALIDATION_ERROR'
        ], 422);
    }

    /**
     * Handle model not found exceptions.
     *
     * @param  \Illuminate\Database\Eloquent\ModelNotFoundException  $exception
     * @return \Illuminate\Http\JsonResponse
     */
    protected function handleModelNotFoundException(ModelNotFoundException $exception): JsonResponse
    {
        $model = class_basename($exception->getModel());
        
        return response()->json([
            'success' => false,
            'message' => "{$model} not found",
            'error_code' => 'RESOURCE_NOT_FOUND'
        ], 404);
    }

    /**
     * Handle authentication exceptions.
     *
     * @param  \Illuminate\Auth\AuthenticationException  $exception
     * @return \Illuminate\Http\JsonResponse
     */
    protected function handleAuthenticationException(AuthenticationException $exception): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => 'Authentication required',
            'error_code' => 'AUTHENTICATION_REQUIRED'
        ], 401);
    }

    /**
     * Handle authorization exceptions.
     *
     * @param  \Illuminate\Auth\Access\AuthorizationException  $exception
     * @return \Illuminate\Http\JsonResponse
     */
    protected function handleAuthorizationException(AuthorizationException $exception): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => 'Insufficient permissions',
            'error_code' => 'INSUFFICIENT_PERMISSIONS'
        ], 403);
    }

    /**
     * Handle HTTP exceptions.
     *
     * @param  \Throwable  $exception
     * @return \Illuminate\Http\JsonResponse
     */
    protected function handleHttpException(Throwable $exception): JsonResponse
    {
        $statusCode = $exception->getStatusCode();
        $message = $this->getHttpErrorMessage($statusCode);

        return response()->json([
            'success' => false,
            'message' => $message,
            'error_code' => 'HTTP_ERROR'
        ], $statusCode);
    }

    /**
     * Handle general exceptions.
     *
     * @param  \Throwable  $exception
     * @return \Illuminate\Http\JsonResponse
     */
    protected function handleGeneralException(Throwable $exception): JsonResponse
    {
        // In production, don't expose internal error details
        $message = app()->environment('production') 
            ? 'An unexpected error occurred' 
            : $exception->getMessage();

        $response = [
            'success' => false,
            'message' => $message,
            'error_code' => 'INTERNAL_ERROR'
        ];

        // Add debug information in non-production environments
        if (!app()->environment('production')) {
            $response['debug'] = [
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTraceAsString()
            ];
        }

        return response()->json($response, 500);
    }

    /**
     * Get human-readable message for HTTP status codes.
     *
     * @param  int  $statusCode
     * @return string
     */
    protected function getHttpErrorMessage(int $statusCode): string
    {
        $messages = [
            400 => 'Bad request',
            401 => 'Unauthorized',
            403 => 'Forbidden',
            404 => 'Resource not found',
            405 => 'Method not allowed',
            408 => 'Request timeout',
            409 => 'Conflict',
            410 => 'Gone',
            422 => 'Unprocessable entity',
            429 => 'Too many requests',
            500 => 'Internal server error',
            502 => 'Bad gateway',
            503 => 'Service unavailable',
            504 => 'Gateway timeout'
        ];

        return $messages[$statusCode] ?? 'HTTP error';
    }
}
