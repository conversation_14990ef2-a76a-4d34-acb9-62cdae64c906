// resources/js/stores/cart.ts
import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'
import axios from 'axios'
import type { 
  CartItem, 
  Product, 
  LoadingState, 
  ErrorState, 
  ApiResponse,
  AddToCartRequest,
  UpdateCartItemRequest,
  CartSummary 
} from '../types'
import { useOrderSlipStore } from './orderSlip'

const STORAGE_KEY = 'restaurant_carts_v1'

export const useCartStore = defineStore('cart', () => {
  // State
  const carts = ref<Record<string, CartItem[]>>({})
  const loading = ref<LoadingState>({})
  const errors = ref<ErrorState>({})

  // Computed
  const activeItems = computed(() => {
    const orderSlipStore = useOrderSlipStore()
    const slipId = orderSlipStore.activeOrderSlipId
    if (!slipId) return []
    return ensureCartForSlip(slipId)
  })

  const itemCount = computed(() => 
    activeItems.value.reduce((sum, item) => sum + item.quantity, 0)
  )

  const subtotal = computed(() => 
    activeItems.value.reduce((sum, item) => sum + (item.price * item.quantity), 0)
  )

  const tax = computed(() => subtotal.value * 0.12) // 12% VAT

  const total = computed(() => subtotal.value + tax.value)

  const isEmpty = computed(() => activeItems.value.length === 0)

  const hasItems = computed(() => !isEmpty.value)

  // Actions
  async function addItem(product: Product, quantity = 1): Promise<boolean> {
    const orderSlipStore = useOrderSlipStore()
    const slipId = orderSlipStore.activeOrderSlipId
    
    if (!slipId) {
      setError('addItem', 'No active order slip')
      return false
    }

    setLoading('addItem', true)
    clearError('addItem')

    try {
      // Add to local cart first for immediate UI feedback
      const items = ensureCartForSlip(slipId)
      const existingItem = items.find(item => item.id === product.id)
      
      if (existingItem) {
        existingItem.quantity += quantity
        existingItem.total = existingItem.price * existingItem.quantity
      } else {
        items.push({
          id: product.id,
          name: product.name,
          price: product.price,
          quantity,
          total: product.price * quantity
        })
      }

      // Sync with backend if we have an osnumber
      if (orderSlipStore.activeSlip?.osnumber) {
        const request: AddToCartRequest = {
          osnumber: orderSlipStore.activeSlip.osnumber,
          product_id: product.id,
          quantity
        }

        const response = await axios.post<ApiResponse>('/api/cart/add', request)
        
        if (!response.data.success) {
          // Revert local changes if API call failed
          if (existingItem) {
            existingItem.quantity -= quantity
            existingItem.total = existingItem.price * existingItem.quantity
          } else {
            carts.value[slipId] = items.filter(item => item.id !== product.id)
          }
          setError('addItem', response.data.message || 'Failed to add item to cart')
          return false
        }
      }

      return true
    } catch (error: any) {
      setError('addItem', error.response?.data?.message || 'Failed to add item to cart')
      return false
    } finally {
      setLoading('addItem', false)
    }
  }

  async function updateItemQuantity(productId: number, quantity: number): Promise<boolean> {
    const orderSlipStore = useOrderSlipStore()
    const slipId = orderSlipStore.activeOrderSlipId
    
    if (!slipId) {
      setError('updateItem', 'No active order slip')
      return false
    }

    setLoading('updateItem', true)
    clearError('updateItem')

    try {
      const items = ensureCartForSlip(slipId)
      const item = items.find(item => item.id === productId)
      
      if (!item) {
        setError('updateItem', 'Item not found in cart')
        return false
      }

      const oldQuantity = item.quantity

      if (quantity <= 0) {
        // Remove item
        carts.value[slipId] = items.filter(item => item.id !== productId)
      } else {
        // Update quantity
        item.quantity = quantity
        item.total = item.price * quantity
      }

      // Sync with backend if we have an osnumber
      if (orderSlipStore.activeSlip?.osnumber) {
        const request: UpdateCartItemRequest = {
          osnumber: orderSlipStore.activeSlip.osnumber,
          product_id: productId,
          quantity
        }

        const response = await axios.post<ApiResponse>('/api/cart/update', request)
        
        if (!response.data.success) {
          // Revert local changes if API call failed
          if (quantity <= 0) {
            items.push(item)
          } else {
            item.quantity = oldQuantity
            item.total = item.price * oldQuantity
          }
          setError('updateItem', response.data.message || 'Failed to update item')
          return false
        }
      }

      return true
    } catch (error: any) {
      setError('updateItem', error.response?.data?.message || 'Failed to update item')
      return false
    } finally {
      setLoading('updateItem', false)
    }
  }

  async function increaseQuantity(productId: number): Promise<boolean> {
    const item = activeItems.value.find(item => item.id === productId)
    if (!item) return false
    return await updateItemQuantity(productId, item.quantity + 1)
  }

  async function decreaseQuantity(productId: number): Promise<boolean> {
    const item = activeItems.value.find(item => item.id === productId)
    if (!item) return false
    return await updateItemQuantity(productId, Math.max(0, item.quantity - 1))
  }

  async function removeItem(productId: number): Promise<boolean> {
    return await updateItemQuantity(productId, 0)
  }

  async function clearCart(): Promise<boolean> {
    const orderSlipStore = useOrderSlipStore()
    const slipId = orderSlipStore.activeOrderSlipId
    
    if (!slipId) {
      setError('clearCart', 'No active order slip')
      return false
    }

    setLoading('clearCart', true)
    clearError('clearCart')

    try {
      // Clear local cart first
      const oldItems = [...(carts.value[slipId] || [])]
      carts.value[slipId] = []

      // Sync with backend if we have an osnumber
      if (orderSlipStore.activeSlip?.osnumber) {
        const response = await axios.post<ApiResponse>('/api/cart/clear', {
          osnumber: orderSlipStore.activeSlip.osnumber
        })
        
        if (!response.data.success) {
          // Revert local changes if API call failed
          carts.value[slipId] = oldItems
          setError('clearCart', response.data.message || 'Failed to clear cart')
          return false
        }
      }

      return true
    } catch (error: any) {
      setError('clearCart', error.response?.data?.message || 'Failed to clear cart')
      return false
    } finally {
      setLoading('clearCart', false)
    }
  }

  async function syncWithBackend(osnumber: string): Promise<void> {
    setLoading('syncCart', true)
    clearError('syncCart')

    try {
      const response = await axios.get<ApiResponse<any>>(`/api/cart/${osnumber}`)
      
      if (response.data.success && response.data.data) {
        // Transform backend data to cart items
        const backendItems = response.data.data.stores?.flatMap((store: any) => 
          store.store_orders?.flatMap((order: any) => 
            order.parent ? [{
              id: order.parent.product_id,
              name: order.parent.product_name,
              price: parseFloat(order.parent.price),
              quantity: order.parent.quantity,
              total: parseFloat(order.parent.amount)
            }] : []
          ) || []
        ) || []

        // Update local cart with backend data
        const orderSlipStore = useOrderSlipStore()
        const slipId = orderSlipStore.activeOrderSlipId
        if (slipId) {
          carts.value[slipId] = backendItems
        }
      }
    } catch (error: any) {
      setError('syncCart', error.response?.data?.message || 'Failed to sync cart')
    } finally {
      setLoading('syncCart', false)
    }
  }

  async function getCartSummary(osnumber: string): Promise<CartSummary | null> {
    setLoading('getCartSummary', true)
    clearError('getCartSummary')

    try {
      const response = await axios.get<ApiResponse<CartSummary>>(`/api/cart/${osnumber}/summary`)
      
      if (response.data.success && response.data.data) {
        return response.data.data
      }
      return null
    } catch (error: any) {
      setError('getCartSummary', error.response?.data?.message || 'Failed to get cart summary')
      return null
    } finally {
      setLoading('getCartSummary', false)
    }
  }

  // Utility functions
  function ensureCartForSlip(slipId: string): CartItem[] {
    if (!carts.value[slipId]) {
      carts.value[slipId] = []
    }
    return carts.value[slipId]
  }

  function getCartForSlip(slipId: string): CartItem[] {
    return carts.value[slipId] || []
  }

  function getItemQuantity(productId: number): number {
    const item = activeItems.value.find(item => item.id === productId)
    return item?.quantity || 0
  }

  function hasItem(productId: number): boolean {
    return activeItems.value.some(item => item.id === productId)
  }

  function setLoading(key: string, value: boolean): void {
    loading.value[key] = value
  }

  function setError(key: string, message: string): void {
    errors.value[key] = message
  }

  function clearError(key: string): void {
    errors.value[key] = null
  }

  function isLoading(key?: string): boolean {
    if (key) return loading.value[key] || false
    return Object.values(loading.value).some(Boolean)
  }

  function getError(key: string): string | null {
    return errors.value[key] || null
  }

  // Storage functions
  function saveToStorage(): void {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(carts.value))
    } catch (e) {
      console.error('Cart save error:', e)
    }
  }

  function loadFromStorage(): void {
    try {
      const raw = localStorage.getItem(STORAGE_KEY)
      if (raw) {
        carts.value = JSON.parse(raw) || {}
      }
    } catch (e) {
      console.error('Cart load error:', e)
    }
  }

  // Watch for changes and save to storage
  watch(carts, saveToStorage, { deep: true })

  // Initialize from storage
  loadFromStorage()

  return {
    // State
    carts,
    loading,
    errors,
    
    // Computed
    activeItems,
    itemCount,
    subtotal,
    tax,
    total,
    isEmpty,
    hasItems,
    
    // Actions
    addItem,
    updateItemQuantity,
    increaseQuantity,
    decreaseQuantity,
    removeItem,
    clearCart,
    syncWithBackend,
    getCartSummary,
    
    // Utilities
    ensureCartForSlip,
    getCartForSlip,
    getItemQuantity,
    hasItem,
    setLoading,
    setError,
    clearError,
    isLoading,
    getError,
    saveToStorage,
    loadFromStorage
  }
})
