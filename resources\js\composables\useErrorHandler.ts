import { ref, computed } from 'vue'
import type { ApiError, ErrorState } from '../types'

/**
 * Composable for centralized error handling
 */
export function useErrorHandler() {
  const errors = ref<Record<string, string | null>>({})
  const globalError = ref<string | null>(null)

  // Set error for a specific operation
  const setError = (operation: string, error: string | Error | null) => {
    if (error === null) {
      errors.value[operation] = null
    } else if (typeof error === 'string') {
      errors.value[operation] = error
    } else if (error instanceof Error) {
      errors.value[operation] = error.message
    } else {
      errors.value[operation] = 'An unknown error occurred'
    }
  }

  // Get error for a specific operation
  const getError = (operation: string): string | null => {
    return errors.value[operation] || null
  }

  // Check if there's an error for a specific operation
  const hasError = (operation: string): boolean => {
    return !!errors.value[operation]
  }

  // Clear error for a specific operation
  const clearError = (operation: string) => {
    errors.value[operation] = null
  }

  // Clear all errors
  const clearAllErrors = () => {
    errors.value = {}
    globalError.value = null
  }

  // Set global error
  const setGlobalError = (error: string | Error | null) => {
    if (error === null) {
      globalError.value = null
    } else if (typeof error === 'string') {
      globalError.value = error
    } else if (error instanceof Error) {
      globalError.value = error.message
    } else {
      globalError.value = 'An unknown error occurred'
    }
  }

  // Handle API errors with standardized format
  const handleApiError = (operation: string, error: any) => {
    console.error(`API Error in ${operation}:`, error)

    if (error.response?.data?.message) {
      setError(operation, error.response.data.message)
    } else if (error.response?.data?.error) {
      setError(operation, error.response.data.error)
    } else if (error.message) {
      setError(operation, error.message)
    } else {
      setError(operation, `Failed to ${operation}. Please try again.`)
    }
  }

  // Handle validation errors
  const handleValidationErrors = (operation: string, validationErrors: Record<string, string[]>) => {
    const errorMessages = Object.entries(validationErrors)
      .map(([field, messages]) => `${field}: ${messages.join(', ')}`)
      .join('; ')
    
    setError(operation, errorMessages)
  }

  // Computed property for any errors
  const hasAnyError = computed(() => {
    return !!globalError.value || Object.values(errors.value).some(error => !!error)
  })

  // Get all current errors
  const getAllErrors = computed(() => {
    const allErrors: string[] = []
    
    if (globalError.value) {
      allErrors.push(globalError.value)
    }
    
    Object.values(errors.value).forEach(error => {
      if (error) {
        allErrors.push(error)
      }
    })
    
    return allErrors
  })

  return {
    errors: computed(() => errors.value),
    globalError: computed(() => globalError.value),
    hasAnyError,
    getAllErrors,
    setError,
    getError,
    hasError,
    clearError,
    clearAllErrors,
    setGlobalError,
    handleApiError,
    handleValidationErrors
  }
}

/**
 * Composable for loading states
 */
export function useLoadingState() {
  const loadingStates = ref<Record<string, boolean>>({})

  const setLoading = (operation: string, loading: boolean) => {
    loadingStates.value[operation] = loading
  }

  const isLoading = (operation?: string): boolean => {
    if (operation) {
      return !!loadingStates.value[operation]
    }
    // Return true if any operation is loading
    return Object.values(loadingStates.value).some(loading => loading)
  }

  const clearLoading = (operation: string) => {
    loadingStates.value[operation] = false
  }

  const clearAllLoading = () => {
    loadingStates.value = {}
  }

  return {
    loadingStates: computed(() => loadingStates.value),
    setLoading,
    isLoading,
    clearLoading,
    clearAllLoading
  }
}

/**
 * Composable for retry logic
 */
export function useRetry() {
  const retryCount = ref<Record<string, number>>({})
  const maxRetries = 3
  const retryDelay = 1000 // 1 second

  const shouldRetry = (operation: string, error: any): boolean => {
    const count = retryCount.value[operation] || 0
    
    // Don't retry validation errors or client errors (4xx)
    if (error.response?.status >= 400 && error.response?.status < 500) {
      return false
    }
    
    return count < maxRetries
  }

  const incrementRetry = (operation: string) => {
    retryCount.value[operation] = (retryCount.value[operation] || 0) + 1
  }

  const resetRetry = (operation: string) => {
    retryCount.value[operation] = 0
  }

  const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

  const withRetry = async <T>(
    operation: string,
    fn: () => Promise<T>,
    onError?: (error: any, attempt: number) => void
  ): Promise<T> => {
    resetRetry(operation)
    
    while (true) {
      try {
        const result = await fn()
        resetRetry(operation)
        return result
      } catch (error) {
        const attempt = (retryCount.value[operation] || 0) + 1
        
        if (onError) {
          onError(error, attempt)
        }
        
        if (!shouldRetry(operation, error)) {
          throw error
        }
        
        incrementRetry(operation)
        await delay(retryDelay * attempt) // Exponential backoff
      }
    }
  }

  return {
    retryCount: computed(() => retryCount.value),
    shouldRetry,
    incrementRetry,
    resetRetry,
    withRetry
  }
}

/**
 * Combined composable for complete error and loading management
 */
export function useAsyncOperation() {
  const errorHandler = useErrorHandler()
  const loadingState = useLoadingState()
  const retry = useRetry()

  const execute = async <T>(
    operation: string,
    fn: () => Promise<T>,
    options: {
      withRetry?: boolean
      onSuccess?: (result: T) => void
      onError?: (error: any) => void
    } = {}
  ): Promise<T | null> => {
    try {
      errorHandler.clearError(operation)
      loadingState.setLoading(operation, true)

      const executeFunction = options.withRetry 
        ? () => retry.withRetry(operation, fn)
        : fn

      const result = await executeFunction()
      
      if (options.onSuccess) {
        options.onSuccess(result)
      }
      
      return result
    } catch (error) {
      errorHandler.handleApiError(operation, error)
      
      if (options.onError) {
        options.onError(error)
      }
      
      return null
    } finally {
      loadingState.setLoading(operation, false)
    }
  }

  return {
    ...errorHandler,
    ...loadingState,
    execute
  }
}
