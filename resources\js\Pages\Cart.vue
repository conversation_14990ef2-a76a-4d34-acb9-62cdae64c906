<template>
    <div class="min-h-screen bg-gray-100">
        <!-- Navigation -->
        <nav class="bg-white shadow sticky top-0 z-10">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center space-x-4">
                        <Button @click="goToDashboard" severity="secondary" outlined>
                            <i class="pi pi-arrow-left mr-2"></i>
                            Back to Menu
                        </Button>
                        <!-- <h1 class="text-xl font-semibold text-gray-900">Shopping Cart</h1> -->
                    </div>
                    <div class="flex items-center space-x-4">
                        <button @click="logout"
                            class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                            Logout
                        </button>
                    </div>
                </div>
            </div>
        </nav>

        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- Cart Items -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-gray-900">Your Cart</h2>
                    <Badge :value="cartItemsCount" severity="info" />
                </div>

                <!-- Cart Items List -->
                <div v-if="cartItems.length > 0" class="space-y-4">
                    <div v-for="item in cartItems" :key="item.id"
                        class="flex justify-between items-center p-4 bg-gray-50 rounded-lg border">
                        <div class="flex-1">
                            <h4 class="font-semibold text-gray-900 text-lg">{{ item.name }}</h4>
                            <p class="text-gray-600">₱{{ item.price.toFixed(2) }} each</p>
                            <p class="text-sm text-gray-500 mt-1">Subtotal: ₱{{ (item.price * item.quantity).toFixed(2) }}</p>
                        </div>
                        <div class="flex items-center space-x-3">
                            <Button icon="pi pi-minus" size="small" severity="secondary" outlined
                                @click="decreaseQuantity(item.id)" 
                                :disabled="item.quantity <= 1" />
                            <span class="font-semibold min-w-[3rem] text-center text-lg">{{ item.quantity }}</span>
                            <Button icon="pi pi-plus" size="small" severity="secondary" outlined
                                @click="increaseQuantity(item.id)" />
                            <Button icon="pi pi-trash" size="small" severity="danger" outlined
                                @click="removeItem(item.id)" 
                                class="ml-4" />
                        </div>
                    </div>

                    <!-- Cart Summary -->
                    <Divider />
                    <div class="bg-gray-50 p-6 rounded-lg">
                        <div class="space-y-3">
                            <div class="flex justify-between text-lg">
                                <span>Items ({{ cartItemsCount }}):</span>
                                <span>₱{{ cartSubtotal.toFixed(2) }}</span>
                            </div>
                            <!-- <div class="flex justify-between text-lg">
                                <span>Tax (12%):</span>
                                <span>₱{{ cartTax.toFixed(2) }}</span>
                            </div> -->
                            <Divider />
                            <div class="flex justify-between text-2xl font-bold text-green-600">
                                <span>Total:</span>
                                <span>₱{{ cartTotal.toFixed(2) }}</span>
                            </div>
                        </div>
                        
                        <div class="mt-6 space-y-3">
                            <Button label="Proceed to Checkout" icon="pi pi-credit-card" 
                                class="w-full" severity="success" size="large"                                                                                                           
                                @click="checkout" />
                            <Button label="Clear Cart" icon="pi pi-trash" 
                                class="w-full" severity="danger" outlined
                                @click="clearCart" />
                        </div>
                    </div>
                </div>

                <!-- Empty Cart -->
                <div v-else class="text-center py-16">
                    <i class="pi pi-shopping-cart text-6xl text-gray-300 mb-6"></i>
                    <h3 class="text-2xl font-semibold text-gray-600 mb-4">Your cart is empty</h3>
                    <p class="text-gray-500 text-lg mb-8">Add some delicious items from our menu!</p>
                    <Button label="Browse Menu" icon="pi pi-arrow-left" 
                        severity="info" size="large"
                        @click="goToDashboard" />
                </div>
            </div>
        </div>                       
    </div>                
</template>

<script setup>
import { computed } from 'vue'
import { router } from '@inertiajs/vue3'
import Button from 'primevue/button'
import Badge from 'primevue/badge'
import Divider from 'primevue/divider'

import { useCartStore } from '../stores/cart'
import { useOrderSlipStore } from '../stores/orderSlip'

// Define props to receive user data from the controller
const props = defineProps({
    user: {
        type: Object,
        required: true
    }
})

const cartStore = useCartStore()
const orderSlipStore = useOrderSlipStore()

const cartItems = computed(() => cartStore.activeItems)
const cartItemsCount = computed(() => cartStore.itemCount)
const cartSubtotal = computed(() => cartItems.value.reduce((t, i) => t + (i.price * i.quantity), 0))
const cartTax = computed(() => cartSubtotal.value * 0.12)
const cartTotal = computed(() => cartSubtotal.value + cartTax.value)

const increaseQuantity = (productId) => cartStore.increase(productId)
const decreaseQuantity = (productId) => cartStore.decrease(productId)
const removeItem = (productId) => {
    // Quick remove by decreasing until gone
    const item = cartItems.value.find(i => i.id === productId)
    if (!item) return
    cartStore.carts[orderSlipStore.activeOrderSlipId] = cartItems.value.filter(i => i.id !== productId)
}
const clearCart = () => cartStore.clearActive()

const checkout = () => {
    if (!orderSlipStore.hasActiveSlip) return alert('No active order slip.')
    if (cartItems.value.length === 0) return alert('Your cart is empty!')
    console.log('Checkout for slip', orderSlipStore.activeOrderSlipId, cartItems.value, cartTotal.value)
    alert('Order placed successfully (stub).')
    cartStore.clearActive()
}

// Navigation functions
const goToDashboard = () => {
    router.get('/dashboard')
}

const logout = () => {
    router.post('/logout', {}, { onSuccess: () => {} })
}
</script>