<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateCartItemRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'osnumber' => 'required|string|max:50',
            'product_id' => 'required|integer|exists:Products,Product_ID',
            'quantity' => 'required|integer|min:0|max:999',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'osnumber.required' => 'Order slip number is required.',
            'osnumber.string' => 'Order slip number must be a valid string.',
            'osnumber.max' => 'Order slip number may not be greater than 50 characters.',
            'product_id.required' => 'Product ID is required.',
            'product_id.integer' => 'Product ID must be a valid integer.',
            'product_id.exists' => 'The specified product does not exist.',
            'quantity.required' => 'Quantity is required.',
            'quantity.integer' => 'Quantity must be a valid integer.',
            'quantity.min' => 'Quantity must be at least 0.',
            'quantity.max' => 'Quantity may not be greater than 999.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'osnumber' => 'order slip number',
            'product_id' => 'product',
            'quantity' => 'quantity',
        ];
    }
}
