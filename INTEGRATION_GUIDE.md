# Restaurant Ordering System - Integration Guide

## Overview

This guide demonstrates how to integrate the comprehensive Pinia state management system with Vue.js components and Laravel backend services for the restaurant ordering system.

## Architecture Summary

### Frontend (Vue.js + Pinia)
- **TypeScript Interfaces**: Comprehensive type definitions in `resources/js/types/index.ts`
- **Pinia Stores**: State management for auth, products, cart, and order slips
- **Vue Components**: Reactive components that integrate with stores

### Backend (Laravel)
- **Service Classes**: Business logic separated into dedicated service classes
- **Controllers**: Thin controllers that delegate to services
- **Request Validation**: Form request classes for input validation
- **API Endpoints**: RESTful API design with standardized responses

## Store Integration Examples

### 1. Authentication Store Usage

```typescript
// In a Vue component
import { useAuthStore } from '../stores/auth'

const authStore = useAuthStore()

// Login
await authStore.login('cashier123', 'password')

// Check authentication status
if (authStore.isAuthenticated) {
  // User is logged in
}

// Access user data
console.log(authStore.user?.name)

// Logout
await authStore.logout()
```

### 2. Product Store Usage

```typescript
// In a Vue component
import { useProductStore } from '../stores/product'

const productStore = useProductStore()

// Load products
await productStore.fetchProducts()

// Access products
const products = productStore.products

// Search by quick code
const product = await productStore.searchByQuickCode('ABC123')

// Check loading state
if (productStore.loading) {
  // Show loading spinner
}
```

### 3. Order Slip Store Usage

```typescript
// In a Vue component
import { useOrderSlipStore } from '../stores/orderSlip'

const orderSlipStore = useOrderSlipStore()

// Create new order slip
const newSlipId = await orderSlipStore.createNewSlip({
  branch_id: 1,
  outlet_id: 1,
  device_id: 1,
  user_id: user.id,
  user_name: user.name
})

// Check for active order slip
if (orderSlipStore.hasActiveSlip) {
  console.log('Active slip:', orderSlipStore.activeSlip)
}

// Attach existing order slip
await orderSlipStore.attachSlip('slip-id')

// Load user's order slips
await orderSlipStore.loadUserOrderSlips(userId, branchId)
```

### 4. Cart Store Usage

```typescript
// In a Vue component
import { useCartStore } from '../stores/cart'

const cartStore = useCartStore()

// Add item to cart
cartStore.addItem(product, quantity)

// Update item quantity
cartStore.increase(productId)
cartStore.decrease(productId)

// Access cart data
const itemCount = cartStore.itemCount
const total = cartStore.total
const items = cartStore.activeItems

// Clear cart
cartStore.clearActive()
```

## Component Integration Patterns

### 1. Dashboard Component Integration

```vue
<template>
  <div>
    <!-- Loading state -->
    <div v-if="productStore.loading">Loading...</div>
    
    <!-- Products grid -->
    <div v-else>
      <div v-for="product in productStore.products" :key="product.id">
        <h3>{{ product.name }}</h3>
        <p>₱{{ product.price }}</p>
        <button @click="addToCart(product)">Add to Cart</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useProductStore } from '../stores/product'
import { useCartStore } from '../stores/cart'

const productStore = useProductStore()
const cartStore = useCartStore()

function addToCart(product: Product) {
  cartStore.addItem(product, 1)
}

onMounted(async () => {
  await productStore.fetchProducts()
})
</script>
```

### 2. Cart Component Integration

```vue
<template>
  <div>
    <!-- Cart items -->
    <div v-for="item in cartStore.activeItems" :key="item.product_id">
      <h4>{{ item.product_name }}</h4>
      <div>
        <button @click="cartStore.decrease(item.product_id)">-</button>
        <span>{{ item.quantity }}</span>
        <button @click="cartStore.increase(item.product_id)">+</button>
      </div>
    </div>
    
    <!-- Cart summary -->
    <div>
      <p>Total Items: {{ cartStore.itemCount }}</p>
      <p>Total: ₱{{ cartStore.total.toFixed(2) }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useCartStore } from '../stores/cart'

const cartStore = useCartStore()
</script>
```

## API Integration Examples

### 1. Authentication API

```typescript
// Login request
POST /api/auth/login
{
  "cashier_number": "cashier123",
  "password": "password"
}

// Response
{
  "success": true,
  "data": {
    "user": { ... },
    "token": "..."
  }
}
```

### 2. Product API

```typescript
// Get products
GET /api/products

// Search by quick code
POST /api/products/search/quick-code
{
  "quick_code": "ABC123"
}

// Get by department
GET /api/products/department/1
```

### 3. Order Slip API

```typescript
// Create order slip
POST /api/orderslips
{
  "branch_id": 1,
  "outlet_id": 1,
  "device_id": 1,
  "user_id": 123,
  "user_name": "John Doe"
}

// Attach order slip
POST /api/orderslips/attach
{
  "orderslip_id": "slip-123"
}
```

### 4. Cart API

```typescript
// Add to cart
POST /api/cart/add
{
  "osnumber": "OS-123",
  "product_id": 456,
  "quantity": 2
}

// Update cart item
POST /api/cart/update
{
  "osnumber": "OS-123",
  "product_id": 456,
  "quantity": 3
}
```

## Error Handling Patterns

### 1. Store-Level Error Handling

```typescript
// In store actions
async login(cashierNumber: string, password: string) {
  try {
    this.setLoading('login', true)
    const response = await api.post('/auth/login', { cashierNumber, password })
    this.user = response.data.user
    this.setError('login', null)
    return true
  } catch (error) {
    this.setError('login', error.message)
    return false
  } finally {
    this.setLoading('login', false)
  }
}
```

### 2. Component-Level Error Handling

```vue
<template>
  <div>
    <!-- Error display -->
    <div v-if="authStore.getError('login')" class="error">
      {{ authStore.getError('login') }}
    </div>
    
    <!-- Login form -->
    <form @submit.prevent="handleLogin">
      <!-- form fields -->
      <button :disabled="authStore.isLoading('login')">
        {{ authStore.isLoading('login') ? 'Logging in...' : 'Login' }}
      </button>
    </form>
  </div>
</template>
```

## Best Practices

### 1. State Management
- Use computed properties for derived state
- Keep actions pure and predictable
- Handle loading and error states consistently
- Use TypeScript for type safety

### 2. Component Design
- Keep components focused and single-purpose
- Use props for data down, events for data up
- Leverage stores for shared state
- Implement proper error boundaries

### 3. API Integration
- Use consistent request/response formats
- Implement proper validation
- Handle errors gracefully
- Use loading states for better UX

### 4. Performance
- Use lazy loading for routes
- Implement proper caching strategies
- Optimize re-renders with computed properties
- Use pagination for large datasets

## Testing Integration

```typescript
// Example store test
import { setActivePinia, createPinia } from 'pinia'
import { useAuthStore } from '../stores/auth'

describe('Auth Store', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  it('should login successfully', async () => {
    const authStore = useAuthStore()
    const result = await authStore.login('test', 'password')
    expect(result).toBe(true)
    expect(authStore.isAuthenticated).toBe(true)
  })
})
```

This integration guide provides comprehensive examples of how to use the Pinia stores with Vue components and Laravel backend services, following best practices for maintainable and scalable code.
