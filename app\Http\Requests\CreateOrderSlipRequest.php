<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CreateOrderSlipRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'branch_id' => 'required|integer|min:1',
            'outlet_id' => 'required|integer|min:1',
            'device_id' => 'required|integer|min:1',
            'user_id' => 'required|integer|exists:Cashiers,Cashier_ID',
            'user_name' => 'required|string|max:255',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'branch_id.required' => 'Branch ID is required.',
            'branch_id.integer' => 'Branch ID must be a valid integer.',
            'branch_id.min' => 'Branch ID must be at least 1.',
            'outlet_id.required' => 'Outlet ID is required.',
            'outlet_id.integer' => 'Outlet ID must be a valid integer.',
            'outlet_id.min' => 'Outlet ID must be at least 1.',
            'device_id.required' => 'Device ID is required.',
            'device_id.integer' => 'Device ID must be a valid integer.',
            'device_id.min' => 'Device ID must be at least 1.',
            'user_id.required' => 'User ID is required.',
            'user_id.integer' => 'User ID must be a valid integer.',
            'user_id.exists' => 'The specified user does not exist.',
            'user_name.required' => 'User name is required.',
            'user_name.string' => 'User name must be a valid string.',
            'user_name.max' => 'User name may not be greater than 255 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'branch_id' => 'branch ID',
            'outlet_id' => 'outlet ID',
            'device_id' => 'device ID',
            'user_id' => 'user ID',
            'user_name' => 'user name',
        ];
    }
}
