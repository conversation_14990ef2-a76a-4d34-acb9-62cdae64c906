// resources/js/stores/orderSlip.js
import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'

const STORAGE_KEY = 'restaurant_order_slips_v1'

export const useOrderSlipStore = defineStore('orderSlip', () => {
  const orderSlips = ref([]) // [{ id, status: 'attached'|'detached', createdAt }]
  const activeOrderSlipId = ref(null)

  const hasActiveSlip = computed(() => !!activeOrderSlipId.value)
  const activeSlip = computed(() => orderSlips.value.find(s => s.id === activeOrderSlipId.value) || null)

  function loadFromStorage() {
    try {
      const raw = localStorage.getItem(STORAGE_KEY)
      if (raw) {
        const data = JSON.parse(raw)
        orderSlips.value = data.orderSlips || []
        activeOrderSlipId.value = data.activeOrderSlipId || null
      }
    } catch (e) {
      console.error('OrderSlip load error:', e)
    }
  }

  function saveToStorage() {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify({
        orderSlips: orderSlips.value,
        activeOrderSlipId: activeOrderSlipId.value,
      }))
    } catch (e) {
      console.error('OrderSlip save error:', e)
    }
  }

  function createNewSlip() {
    const id = Date.now().toString()
    const slip = { id, status: 'attached', createdAt: new Date().toISOString() }
    orderSlips.value.unshift(slip)
    activeOrderSlipId.value = id
    return id
  }

  function attachSlip(id) {
    const slip = orderSlips.value.find(s => s.id === id)
    if (!slip) return
    orderSlips.value.forEach(s => { if (s.id === activeOrderSlipId.value) s.status = 'detached' })
    slip.status = 'attached'
    activeOrderSlipId.value = slip.id
  }

  function detachActiveSlip() {
    const slip = orderSlips.value.find(s => s.id === activeOrderSlipId.value)
    if (slip) slip.status = 'detached'
    activeOrderSlipId.value = null
  }

  function switchActive(id) {
    if (id === activeOrderSlipId.value) return
    attachSlip(id)
  }

  function removeSlip(id) {
    if (id === activeOrderSlipId.value) {
      activeOrderSlipId.value = null
    }
    orderSlips.value = orderSlips.value.filter(s => s.id !== id)
  }

  watch([orderSlips, activeOrderSlipId], saveToStorage, { deep: true })
  loadFromStorage()

  return {
    orderSlips,
    activeOrderSlipId,
    hasActiveSlip,
    activeSlip,
    createNewSlip,
    attachSlip,
    detachActiveSlip,
    switchActive,
    removeSlip,
  }
})