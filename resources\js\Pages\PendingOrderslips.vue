<template>
  <div class="min-h-screen bg-gray-100">
    <!-- Navigation -->
    <NavBar 
      :user="user"
      :cartItemsCount="cartStore.itemCount"
      @nav-table="() => {}"
      @nav-orderslips="() => {}"
      @nav-cart="goToCart"
      @logout="logout"
    />

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex justify-between items-center mb-6">
          <h1 class="text-2xl font-bold text-gray-900">Order Slips</h1>
          <Button 
            @click="createNewOrderSlip"
            :loading="orderSlipStore.isLoading('createSlip')"
            :disabled="orderSlipStore.isLoading()"
            class="bg-blue-600 hover:bg-blue-700"
          >
            <i class="pi pi-plus mr-2"></i>
            New Transaction
          </Button>
        </div>

        <!-- Error Messages -->
        <div v-if="orderSlipStore.getError('createSlip')" class="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
          <p class="text-red-800">{{ orderSlipStore.getError('createSlip') }}</p>
        </div>

        <!-- Loading State -->
        <div v-if="orderSlipStore.isLoading('loadSlips')" class="flex justify-center items-center py-12">
          <ProgressSpinner />
        </div>

        <!-- Order Slips List -->
        <div v-else-if="orderSlipStore.orderSlips.length > 0" class="space-y-4">
          <div 
            v-for="slip in orderSlipStore.orderSlips" 
            :key="slip.id"
            class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
            :class="{
              'border-blue-500 bg-blue-50': slip.is_active,
              'border-gray-200': !slip.is_active
            }"
          >
            <div class="flex justify-between items-start">
              <div class="flex-1">
                <div class="flex items-center space-x-3 mb-2">
                  <h3 class="text-lg font-semibold text-gray-900">
                    {{ slip.osnumber || `Order #${slip.id}` }}
                  </h3>
                  <Badge 
                    :value="slip.status" 
                    :severity="getStatusSeverity(slip.status)"
                  />
                  <Badge 
                    v-if="slip.is_active"
                    value="Active" 
                    severity="success"
                  />
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                  <div>
                    <span class="font-medium">Created:</span>
                    {{ formatDate(slip.created_at) }}
                  </div>
                  <div v-if="slip.total_amount">
                    <span class="font-medium">Total:</span>
                    ₱{{ slip.total_amount.toFixed(2) }}
                  </div>
                  <div v-if="slip.completed_at">
                    <span class="font-medium">Completed:</span>
                    {{ formatDate(slip.completed_at) }}
                  </div>
                </div>
              </div>

              <div class="flex space-x-2 ml-4">
                <Button
                  v-if="!slip.is_active && slip.status !== 'completed'"
                  @click="attachOrderSlip(slip.id)"
                  :loading="orderSlipStore.isLoading('attachSlip')"
                  size="small"
                  class="bg-green-600 hover:bg-green-700"
                >
                  Attach
                </Button>
                
                <Button
                  v-if="slip.is_active"
                  @click="detachOrderSlip()"
                  :loading="orderSlipStore.isLoading('detachSlip')"
                  size="small"
                  severity="secondary"
                >
                  Detach
                </Button>

                <Button
                  @click="viewOrderSlip(slip)"
                  size="small"
                  severity="info"
                  outlined
                >
                  View
                </Button>

                <Button
                  v-if="slip.status !== 'completed' && !slip.is_active"
                  @click="removeOrderSlip(slip.id)"
                  size="small"
                  severity="danger"
                  outlined
                >
                  Remove
                </Button>
              </div>
            </div>
          </div>
        </div>

        <!-- Empty State -->
        <div v-else class="text-center py-12">
          <div class="text-gray-400 mb-4">
            <i class="pi pi-file-o text-6xl"></i>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">No Order Slips</h3>
          <p class="text-gray-600 mb-4">Create your first order slip to get started.</p>
          <Button 
            @click="createNewOrderSlip"
            :loading="orderSlipStore.isLoading('createSlip')"
            class="bg-blue-600 hover:bg-blue-700"
          >
            <i class="pi pi-plus mr-2"></i>
            Create New Order Slip
          </Button>
        </div>
      </div>
    </div>

    <!-- Active Order Slip Actions -->
    <div 
      v-if="orderSlipStore.hasActiveSlip" 
      class="fixed bottom-6 right-6 bg-white rounded-lg shadow-lg p-4 border border-gray-200"
    >
      <div class="text-sm text-gray-600 mb-2">Active Order Slip</div>
      <div class="font-semibold text-gray-900 mb-3">
        {{ orderSlipStore.activeSlip?.osnumber || `Order #${orderSlipStore.activeSlip?.id}` }}
      </div>
      <div class="flex space-x-2">
        <Button
          @click="goToDashboard"
          size="small"
          class="bg-blue-600 hover:bg-blue-700"
        >
          <i class="pi pi-shopping-cart mr-2"></i>
          Add Items
        </Button>
        <Button
          @click="goToCart"
          size="small"
          severity="secondary"
        >
          <i class="pi pi-list mr-2"></i>
          View Cart ({{ cartStore.itemCount }})
        </Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { router } from '@inertiajs/vue3'
import Button from 'primevue/button'
import Badge from 'primevue/badge'
import ProgressSpinner from 'primevue/progressspinner'
import NavBar from '../Components/NavBar.vue'
import { useOrderSlipStore } from '../stores/orderSlip'
import { useCartStore } from '../stores/cart'
import { useAuthStore } from '../stores/auth'
import type { User, OrderSlip } from '../types'

// Props
const props = defineProps<{
  user: User
}>()

// Stores
const orderSlipStore = useOrderSlipStore()
const cartStore = useCartStore()
const authStore = useAuthStore()

// Methods
async function createNewOrderSlip() {
  const newSlipId = await orderSlipStore.createNewSlip({
    branch_id: 1, // You might want to get this from user session
    outlet_id: 1,
    device_id: 1,
    user_id: props.user.id,
    user_name: props.user.name
  })
  
  if (newSlipId) {
    // Redirect to dashboard to start adding items
    router.get('/dashboard')
  }
}

async function attachOrderSlip(slipId: string) {
  const success = await orderSlipStore.attachSlip(slipId)
  if (success) {
    // Optionally redirect to dashboard
    // router.get('/dashboard')
  }
}

async function detachOrderSlip() {
  await orderSlipStore.detachActiveSlip()
}

function removeOrderSlip(slipId: string) {
  orderSlipStore.removeSlip(slipId)
}

function viewOrderSlip(slip: OrderSlip) {
  // Navigate to order slip details or cart view
  if (slip.osnumber) {
    router.get(`/cart?orderslip=${slip.osnumber}`)
  }
}

function goToDashboard() {
  router.get('/dashboard')
}

function goToCart() {
  router.get('/cart')
}

function logout() {
  authStore.logout().then(() => {
    router.post('/logout')
  })
}

function getStatusSeverity(status: string) {
  switch (status) {
    case 'completed': return 'success'
    case 'paid': return 'info'
    case 'pending': return 'warning'
    case 'active': return 'success'
    case 'attached': return 'success'
    default: return 'secondary'
  }
}

function formatDate(dateString: string) {
  return new Date(dateString).toLocaleString()
}

// Lifecycle
onMounted(async () => {
  // Load user's order slips
  await orderSlipStore.loadUserOrderSlips(props.user.id, 1) // branch_id = 1
  
  // Check for active order slip
  await orderSlipStore.getUserActiveOrderSlip(props.user.id, 1)
})
</script>

<style scoped>
/* Add any component-specific styles here */
</style>
