<?php

namespace App\Http\Controllers;

use App\Http\Services\AuthService;
use App\Http\Traits\ApiResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class AuthController extends Controller
{
    use ApiResponseTrait;

    public function __construct(
        protected AuthService $authService
    ) {}

    /**
     * Authenticate user
     */
    public function login(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'cashier_number' => 'required|string',
                'password' => 'required|string'
            ]);

            $result = $this->authService->authenticate($request);

            if ($result['success']) {
                return $this->successResponse($result['data'], 'Login successful');
            }

            return $this->unauthorizedResponse($result['message'] ?? 'Invalid credentials');
        } catch (\Exception $e) {
            return $this->serverErrorResponse('Login failed: ' . $e->getMessage());
        }
    }

    /**
     * Get current authenticated user
     */
    public function getCurrentUser(): JsonResponse
    {
        $result = $this->authService->getCurrentUser();
        return response()->json($result, $result['success'] ? 200 : 401);
    }

    /**
     * Logout user
     */
    public function logout(): JsonResponse
    {
        $result = $this->authService->logout();
        return response()->json($result);
    }

    /**
     * Check authentication status
     */
    public function checkAuth(): JsonResponse
    {
        $result = $this->authService->isAuthenticated();
        return response()->json($result);
    }

    /**
     * Update user session data
     */
    public function updateSession(Request $request): JsonResponse
    {
        $request->validate([
            'branch_id' => 'nullable|integer',
            'outlet_id' => 'nullable|integer',
            'device_id' => 'nullable|integer'
        ]);

        $result = $this->authService->updateSession($request);
        return response()->json($result, $result['success'] ? 200 : 401);
    }

    /**
     * Get user permissions
     */
    public function getPermissions(): JsonResponse
    {
        $result = $this->authService->getUserPermissions();
        return response()->json($result, $result['success'] ? 200 : 401);
    }

    /**
     * Validate current session
     */
    public function validateSession(): JsonResponse
    {
        $result = $this->authService->validateSession();
        return response()->json($result, $result['success'] ? 200 : 401);
    }
}
