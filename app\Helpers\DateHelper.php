<?php

if (!function_exists('getClarionDate')) {
    /**
     * Convert a Carbon/DateTime instance to Clarion date format
     * Clarion date is typically the number of days since January 1, 1801
     * 
     * @param \Carbon\Carbon|\DateTime|string $date
     * @return int
     */
    function getClarionDate($date): int
    {
        if (is_string($date)) {
            $date = \Carbon\Carbon::parse($date);
        } elseif ($date instanceof \DateTime) {
            $date = \Carbon\Carbon::instance($date);
        }
        
        // Clarion date epoch: January 1, 1801
        $clarionEpoch = \Carbon\Carbon::create(1801, 1, 1);
        
        // Calculate the difference in days
        $daysDifference = $date->diffInDays($clarionEpoch, false);
        
        // Return the absolute value as Clarion date
        return abs($daysDifference);
    }
}
