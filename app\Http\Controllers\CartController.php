<?php

namespace App\Http\Controllers;

use App\Http\Services\CartService;
use App\Http\Requests\AddToCartRequest;
use App\Http\Requests\UpdateCartItemRequest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class CartController extends Controller
{
    public function __construct(
        protected CartService $cartService
    ) {}

    /**
     * Get cart for a specific orderslip
     */
    public function show(string $orderslip_code): JsonResponse
    {
        $result = $this->cartService->getCart($orderslip_code);
        return response()->json($result);
    }

    /**
     * Add item to cart
     */
    public function addItem(AddToCartRequest $request): JsonResponse
    {
        $result = $this->cartService->addItem($request);
        return response()->json($result, $result['success'] ? 200 : 400);
    }

    /**
     * Update item quantity in cart
     */
    public function updateItem(UpdateCartItemRequest $request): JsonResponse
    {
        $result = $this->cartService->updateItemQuantity($request);
        return response()->json($result, $result['success'] ? 200 : 400);
    }

    /**
     * Remove item from cart
     */
    public function removeItem(Request $request): JsonResponse
    {
        $request->validate([
            'osnumber' => 'required|string',
            'product_id' => 'required|integer'
        ]);

        $result = $this->cartService->removeItem($request);
        return response()->json($result, $result['success'] ? 200 : 400);
    }

    /**
     * Clear entire cart
     */
    public function clearCart(Request $request): JsonResponse
    {
        $request->validate([
            'osnumber' => 'required|string'
        ]);

        $result = $this->cartService->clearCart($request);
        return response()->json($result, $result['success'] ? 200 : 400);
    }

    /**
     * Get cart summary (items count, total, etc.)
     */
    public function getSummary(string $orderslip_code): JsonResponse
    {
        $result = $this->cartService->getCart($orderslip_code);

        if (!$result['success']) {
            return response()->json($result, 400);
        }

        $data = $result['data'];
        $summary = [
            'orderslip_code' => $orderslip_code,
            'items_count' => $data['items_count'] ?? 0,
            'total_amount' => $data['total_amount'] ?? '0.00',
            'net_amount' => $data['net_amount'] ?? '0.00',
            'service_charge_amount' => $data['service_charge_amount'] ?? '0.00',
            'status' => $data['status'] ?? 'pending'
        ];

        return response()->json([
            'success' => true,
            'data' => $summary
        ]);
    }
}