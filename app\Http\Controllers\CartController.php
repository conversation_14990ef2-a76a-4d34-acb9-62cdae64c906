<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class CartController extends Controller
{
    // NOTE: These are scaffolds. Wire them to your services/models as needed.

    /**
     * Get cart for a specific orderslip.
     */
    public function show(string $orderslip_code): JsonResponse
    {
        // TODO: Replace with real cart fetch by $orderslip_code
        return response()->json([
            'orderslip_code' => $orderslip_code,
            'items' => [],
            'total' => 0,
        ]);
    }

    /**
     * Add an item to the orderslip cart.
     */
    public function addItem(Request $request, string $orderslip_code): JsonResponse
    {
        $validated = $request->validate([
            'product_id' => ['required', 'integer'],
            'quantity' => ['nullable', 'integer', 'min:1'],
        ]);

        // TODO: Persist item for $orderslip_code
        return response()->json([
            'message' => 'Item added',
            'orderslip_code' => $orderslip_code,
            'item' => $validated,
        ], 201);
    }

    /**
     * Update an item quantity in the orderslip cart.
     */
    public function updateItem(Request $request, string $orderslip_code, int $productId): JsonResponse
    {
        $validated = $request->validate([
            'quantity' => ['required', 'integer', 'min:1'],
        ]);

        // TODO: Update item for $orderslip_code and $productId
        return response()->json([
            'message' => 'Item updated',
            'orderslip_code' => $orderslip_code,
            'product_id' => $productId,
            'quantity' => $validated['quantity'],
        ]);
    }

    /**
     * Remove an item from the orderslip cart.
     */
    public function removeItem(string $orderslip_code, int $productId): JsonResponse
    {
        // TODO: Remove item for $orderslip_code and $productId
        return response()->json([
            'message' => 'Item removed',
            'orderslip_code' => $orderslip_code,
            'product_id' => $productId,
        ]);
    }

    /**
     * Clear the cart for an orderslip.
     */
    public function clear(string $orderslip_code): JsonResponse
    {
        // TODO: Clear cart for $orderslip_code
        return response()->json([
            'message' => 'Cart cleared',
            'orderslip_code' => $orderslip_code,
        ]);
    }
}