<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class StoreController extends Controller
{
    // Simple endpoints to sync Pinia state with backend if desired

    /**
     * Create a new orderslip and return its code/id.
     */
    public function createOrderslip(Request $request): JsonResponse
    {
        // TODO: Create real orderslip using models/services
        $orderslip_code = (string) now()->timestamp; // placeholder
        return response()->json([
            'orderslip_code' => $orderslip_code,
        ], 201);
    }

    /**
     * Attach current user to an orderslip (set as active server-side).
     */
    public function attach(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'orderslip_code' => ['required', 'string'],
        ]);
        // TODO: Implement attach logic
        return response()->json([
            'message' => 'Attached',
            'orderslip_code' => $validated['orderslip_code'],
        ]);
    }

    /**
     * Detach current user from orderslip.
     */
    public function detach(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'orderslip_code' => ['required', 'string'],
        ]);
        // TODO: Implement detach logic
        return response()->json([
            'message' => 'Detached',
            'orderslip_code' => $validated['orderslip_code'],
        ]);
    }
}