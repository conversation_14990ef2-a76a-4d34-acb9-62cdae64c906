<?php

use App\Http\Controllers\LoginController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\OrderSlipController;
use App\Http\Controllers\CurrentTransactionController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use Inertia\Response;

Route::middleware('guest')->group(function (): void {
    Route::get('/', [LoginController::class, 'index'])->name('login');
    Route::post('/login', [LoginController::class, 'login']);
});

Route::middleware('auth')->group(function (): void {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::post('/logout', [LoginController::class, 'logout'])->name('logout');

    // Product routes for web interface
    Route::get('/products', [ProductController::class, 'index'])->name('products.index');

    // Cart page
    Route::get('/cart', function (): Response {
        return Inertia::render('Cart');
    })->name('cart');

    // Order slip pages
    Route::get('/pending-orderslips', [OrderSlipController::class, 'pendingOrderslipsPage'])->name('pending-orderslips');

    // Order slip flow page (main ordering interface)
    Route::get('/order-flow', function (): Response {
        return Inertia::render('OrderslipFlow', [
            'user' => Auth::user()
        ]);
    })->name('order-flow');

    
    Route::get('/orderslips', [App\Http\Controllers\OrderslipController::class, 'index'])->name('orderslips');
    Route::get('/orderslips/{orderslip_code}', [App\Http\Controllers\OrderslipController::class, 'orderslipOverview'])->name('orderslips.overview');
    Route::post('/orderslips', [App\Http\Controllers\OrderslipController::class, 'store']);
    // OrderSlip API routes
    Route::post('/current-transaction', [CurrentTransactionController::class, 'currentTransaction']);
    Route::post('/detach-current-transaction', [CurrentTransactionController::class, 'detachCurrentTransaction']);
    Route::post('/attach-current-transaction', [CurrentTransactionController::class, 'attachCurrentTransaction']);

});

require __DIR__.'/auth.php';