<?php

namespace App\Http\Services;
use App\Http\Resources\OrderslipHeaderResource as ResourceOrderslipHeader;
use App\Models\OrderSlipHeader;

class getCurrentTrasancationService
{
     public function handle($branch_id, $user_id):array
    {
        $item = OrderSlipHeader::where('Branch_ID', $branch_id)->where('Cashier_ID', $user_id)->first();
        if (!$item) {
            return [
                'success' => false,
                'message' => 'Resource not found'
            ];
        }

        return [
            'success' => true,
            'message' => 'Ok',
            'data' => $item->toArray()
        ];
    }
}