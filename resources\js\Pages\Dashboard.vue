<template>
    <div class="min-h-screen bg-gray-100">
        <!-- Navigation -->
        <NavBar 
            :user="props.user"
            :cartItemsCount="cartItemsCount"
            @nav-table="() => {}"
            @nav-orderslips="goToOrderslips"
            @nav-cart="goToCart"
            @logout="logout"
        />

       <div class="w-full mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <!-- <div class="grid grid-cols-1 lg:grid-cols-3 gap-8"> -->
                <!-- Left Side: Product List -->
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-lg shadow p-6">
                        <h2 class="text-2xl font-bold text-gray-900 mb-6">Menu Items</h2>

                        <!-- Loading State -->
                        <div v-if="productStore.loading" class="flex justify-center items-center py-12">
                            <ProgressSpinner />
                        </div>

                        <!-- Products Grid -->
                        <div v-else class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                            <Card v-for="product in productStore.products" :key="product.id"
                                class="shadow-md hover:shadow-lg transition-shadow cursor-pointer"
                                @click="openDialog(product)">
                                <template #header>
                                    <img v-if="product.image" :src="product.image" :alt="product.name"
                                    class="w-full h-48 object-cover" />
                                </template>
                                <template #title>
                                    <h3 class="text-lg font-semibold text-gray-900">{{ product.name }}</h3>
                                </template>
                                <template #content>
                                    <p class="text-gray-700 text-sm mb-4">{{ product.description }}</p>
                                    <div class="flex justify-between items-center">
                                        <span class="text-2xl font-bold text-green-600">P{{ product.price.toFixed(2)
                                            }}</span>
                                    </div>
                                </template>
                            </Card>
                        </div>
                 
                        <Dialog v-model:visible="showDialog" modal header="Add to Cart" :style="{ width: '500px' }">
                            <div v-if="selectedProduct">
                                <div class="mb-4">
                                    <div class="font-semibold text-lg mb-2">{{ selectedProduct.name }}</div>
                                    <div class="mb-2">Price: <span class="font-bold">P{{ selectedProduct.price.toFixed(2) }}</span></div>
                                    <div class="flex items-center space-x-2">
                                        <label for="quantity" class="block">Quantity:</label>
                                        <InputNumber v-model="dialogQuantity" inputId="quantity" :min="1" :max="999" />
                                    </div>
                                </div>
                                <Button label="Add to Cart" icon="pi pi-cart-plus" class="w-full" @click="addToCart" />
                            </div>
                        </Dialog>

                        <!-- Empty State -->
                        <div v-if="!productStore.loading && productStore.products.length === 0" class="text-center py-12">
                            <i class="pi pi-shopping-cart text-6xl text-gray-300 mb-4"></i>
                            <h3 class="text-xl font-semibold text-gray-600 mb-2">No products available</h3>
                            <p class="text-gray-500">Check back later for new menu items.</p>
                        </div>
                    </div>
                </div>

                <!-- Right Side: Cart/Selection Panel -->
                <!-- <div class="lg:col-span-1">
                    <div class="bg-white rounded-lg shadow p-16 sticky top-8 ">
                        <div class="flex justify-between items-center mb-6">
                            <h2 class="text-xl font-bold text-gray-900">Transaction Summary</h2>
                            <Badge :value="cartItemsCount" severity="info" />
                        </div> -->

                        <!-- Cart Items -->
                        <!-- <div v-if="cartItems.length > 0" class="space-y-4">
                            <div v-for="item in cartItems" :key="item.id"
                                class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                <div class="flex-1">
                                    <h4 class="font-medium text-gray-900">{{ item.name }}</h4>
                                    <p class="text-sm text-gray-600">P{{ item.price.toFixed(2) }} each</p>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <Button icon="pi pi-minus" size="small" severity="secondary" outlined
                                        @click="decreaseQuantity(item.id)" />
                                    <span class="font-semibold min-w-[2rem] text-center">{{ item.quantity }}</span>
                                    <Button icon="pi pi-plus" size="small" severity="secondary" outlined
                                        @click="increaseQuantity(item.id)" />
                                </div>
                            </div> -->

                            <!-- Cart Summary -->
                            <!-- <Divider />
                            <div class="space-y-2">
                                <div class="flex justify-between text-lg font-semibold">
                                    <span>Total:</span>
                                    <span class="text-green-600">${{ cartTotal.toFixed(2) }}</span>
                                </div>
                                <Button label="Checkout" icon="pi pi-credit-card" class="w-full" severity="success"
                                    @click="checkout" />
                                <Button label="Clear Cart" icon="pi pi-trash" class="w-full" severity="danger" outlined
                                    @click="clearCart" />
                            </div>
                        </div> -->

                        <!-- Empty Cart -->
                        <!-- <div v-else class="text-center py-8">
                            <i class="pi pi-shopping-cart text-4xl text-gray-300 mb-4"></i>
                            <h3 class="text-lg font-semibold text-gray-600 mb-2">Your cart is empty</h3>
                            <p class="text-gray-500 text-sm">Add some delicious items from the menu!</p>
                        </div>
                    </div>
                </div> -->
            <!-- </div> -->
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { router } from '@inertiajs/vue3'
import Card from 'primevue/card'
import Button from 'primevue/button'
import Badge from 'primevue/badge'
import ProgressSpinner from 'primevue/progressspinner'
import Divider from 'primevue/divider'
import Dialog from 'primevue/dialog'
import InputNumber from 'primevue/inputnumber'
import NavBar from '../Components/NavBar.vue'
import { useProductStore } from '../stores/product'
import { useCartStore } from '../stores/cart'
import { useOrderSlipStore } from '../stores/orderSlip'
import { useAuthStore } from '../stores/auth'
import type { User, Product } from '../types'

// Define props to receive user data from the controller
const props = defineProps<{
    user: User
}>()

// Stores
const productStore = useProductStore()
const cartStore = useCartStore()
const orderSlipStore = useOrderSlipStore()
const authStore = useAuthStore()

// Reactive data
const selectedProduct = ref<Product | null>(null)
const dialogQuantity = ref(1)

// Dialog state
const showDialog = ref(false)

// Computed properties
const cartItemsCount = computed(() => cartStore.itemCount)
const cartTotal = computed(() => cartStore.total)

// Navigation functions
const goToCart = () => {
    router.get('/cart')
}

const goToOrderslips = () => {
    router.get('/pending-orderslips')
}

// Methods
const openDialog = (product: Product) => {
    selectedProduct.value = product
    dialogQuantity.value = 1
    showDialog.value = true
}

const closeDialog = () => {
    showDialog.value = false
    selectedProduct.value = null
    dialogQuantity.value = 1
}

const addToCart = () => {
    if (!selectedProduct.value || !orderSlipStore.hasActiveSlip) {
        alert('Please create or select an active order slip first!')
        router.get('/pending-orderslips')
        return
    }

    cartStore.addItem(selectedProduct.value, dialogQuantity.value)
    closeDialog()
}

const logout = () => {
    authStore.logout().then(() => {
        router.post('/logout')
    })
}

// Lifecycle hooks
onMounted(async () => {
    // Load products
    await productStore.fetchProducts()

    // For now, we'll assume there's an active order slip
    // In a real app, you'd check for active order slip here
})
</script>