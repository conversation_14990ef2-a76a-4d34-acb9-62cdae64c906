<?php

namespace App\Http\Services;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Exception;

class AuthService
{
    /**
     * Authenticate user and return user data
     */
    public function authenticate(Request $request)
    {
        try {
            $credentials = [
                'Cashier_Number' => $request->cashier_number,
                'password' => $request->password
            ];

            if (Auth::attempt($credentials)) {
                $user = Auth::user();
                
                if (!$user->isActive()) {
                    Auth::logout();
                    return [
                        'success' => false,
                        'message' => 'User account is inactive'
                    ];
                }

                return [
                    'success' => true,
                    'message' => 'Authentication successful',
                    'data' => [
                        'user' => $this->formatUserData($user),
                        'session_id' => session()->getId()
                    ]
                ];
            }

            return [
                'success' => false,
                'message' => 'Invalid credentials'
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Authentication failed',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get current authenticated user
     */
    public function getCurrentUser()
    {
        try {
            $user = Auth::user();

            if (!$user) {
                return [
                    'success' => false,
                    'message' => 'User not authenticated'
                ];
            }

            return [
                'success' => true,
                'data' => $this->formatUserData($user)
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to get current user',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Logout user
     */
    public function logout()
    {
        try {
            Auth::logout();
            session()->invalidate();
            session()->regenerateToken();

            return [
                'success' => true,
                'message' => 'Logout successful'
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Logout failed',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Check if user is authenticated
     */
    public function isAuthenticated()
    {
        return [
            'success' => true,
            'data' => [
                'authenticated' => Auth::check(),
                'user' => Auth::check() ? $this->formatUserData(Auth::user()) : null
            ]
        ];
    }

    /**
     * Update user session data
     */
    public function updateSession(Request $request)
    {
        try {
            $user = Auth::user();

            if (!$user) {
                return [
                    'success' => false,
                    'message' => 'User not authenticated'
                ];
            }

            // Update session data if needed
            if ($request->has('branch_id')) {
                session(['current_branch_id' => $request->branch_id]);
            }

            if ($request->has('outlet_id')) {
                session(['current_outlet_id' => $request->outlet_id]);
            }

            if ($request->has('device_id')) {
                session(['current_device_id' => $request->device_id]);
            }

            return [
                'success' => true,
                'message' => 'Session updated successfully',
                'data' => [
                    'user' => $this->formatUserData($user),
                    'session_data' => [
                        'branch_id' => session('current_branch_id'),
                        'outlet_id' => session('current_outlet_id'),
                        'device_id' => session('current_device_id')
                    ]
                ]
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to update session',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get user permissions and role information
     */
    public function getUserPermissions()
    {
        try {
            $user = Auth::user();

            if (!$user) {
                return [
                    'success' => false,
                    'message' => 'User not authenticated'
                ];
            }

            $role = $user->role;

            return [
                'success' => true,
                'data' => [
                    'user_id' => $user->Cashier_ID,
                    'role' => [
                        'id' => $role->Cashier_Role_ID ?? null,
                        'name' => $role->Cashier_Role_Desc ?? 'Unknown Role'
                    ],
                    'permissions' => $this->getUserRolePermissions($role)
                ]
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to get user permissions',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Validate user session
     */
    public function validateSession()
    {
        try {
            if (!Auth::check()) {
                return [
                    'success' => false,
                    'message' => 'Session expired or invalid'
                ];
            }

            $user = Auth::user();

            if (!$user->isActive()) {
                Auth::logout();
                return [
                    'success' => false,
                    'message' => 'User account has been deactivated'
                ];
            }

            return [
                'success' => true,
                'message' => 'Session is valid',
                'data' => $this->formatUserData($user)
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Session validation failed',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Format user data for API responses
     */
    private function formatUserData($user)
    {
        return [
            'id' => $user->Cashier_ID,
            'cashier_number' => $user->Cashier_Number,
            'name' => $user->Cashier_Name,
            'role_id' => $user->Cashier_Role_ID,
            'is_active' => $user->isActive(),
            'role' => $user->role ? [
                'id' => $user->role->Cashier_Role_ID,
                'name' => $user->role->Cashier_Role_Desc ?? 'Unknown Role'
            ] : null
        ];
    }

    /**
     * Get user role permissions (placeholder for future implementation)
     */
    private function getUserRolePermissions($role)
    {
        // This is a placeholder - implement based on your role/permission system
        if (!$role) {
            return [];
        }

        // Default permissions based on role
        $permissions = [
            'can_create_orders' => true,
            'can_modify_orders' => true,
            'can_void_items' => false,
            'can_apply_discounts' => false,
            'can_view_reports' => false
        ];

        // Customize permissions based on role ID
        switch ($role->Cashier_Role_ID) {
            case 1: // Admin
                $permissions = array_map(fn() => true, $permissions);
                break;
            case 2: // Manager
                $permissions['can_void_items'] = true;
                $permissions['can_apply_discounts'] = true;
                $permissions['can_view_reports'] = true;
                break;
            case 3: // Cashier
                // Default permissions already set
                break;
        }

        return $permissions;
    }
}
