<?php

namespace App\Http\Services;

use App\Models\OrderSlipDetail;
use App\Models\OrderSlipHeader;
use App\Models\Product;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Exception;

class CartService
{
    public function __construct(
        protected CartInformationService $cartInformationService
    ) {}

    /**
     * Add item to cart (order slip)
     */
    public function addItem(Request $request)
    {
        try {
            DB::beginTransaction();

            $orderSlip = OrderSlipHeader::where('OSNUMBER', $request->osnumber)->first();

            if (!$orderSlip) {
                DB::rollBack();
                return [
                    'success' => false,
                    'message' => 'Order slip not found'
                ];
            }

            $product = Product::where('Product_ID', $request->product_id)->first();

            if (!$product) {
                DB::rollBack();
                return [
                    'success' => false,
                    'message' => 'Product not found'
                ];
            }

            // Check if item already exists in cart
            $existingItem = OrderSlipDetail::where('OSNUMBER', $request->osnumber)
                ->where('PRODUCT_ID', $request->product_id)
                ->where('STATUS', '!=', 'V') // Not voided
                ->first();

            if ($existingItem) {
                // Update quantity
                $newQuantity = $existingItem->QUANTITY + ($request->quantity ?? 1);
                $newAmount = $newQuantity * $product->Product_Price;

                $existingItem->update([
                    'QUANTITY' => $newQuantity,
                    'AMOUNT' => $newAmount,
                    'NETAMOUNT' => $newAmount
                ]);

                $updatedItem = $existingItem->fresh();
            } else {
                // Create new cart item
                $quantity = $request->quantity ?? 1;
                $amount = $quantity * $product->Product_Price;

                $payload = [
                    'OSNUMBER' => $request->osnumber,
                    'ORDERSLIPNO' => $orderSlip->ORDERSLIPNO,
                    'ORDERSLIPDETAILID' => OrderSlipDetail::getNewDetailId($request->osnumber),
                    'BRANCHID' => $orderSlip->BRANCHID,
                    'OUTLETID' => $orderSlip->OUTLETID,
                    'DEVICENO' => $orderSlip->DEVICENO,
                    'PRODUCT_ID' => $request->product_id,
                    'MAIN_PRODUCT_ID' => $request->product_id,
                    'RETAILPRICE' => $product->Product_Price,
                    'QUANTITY' => $quantity,
                    'REQUESTEDQTY' => $quantity,
                    'AMOUNT' => $amount,
                    'NETAMOUNT' => $amount,
                    'LINE_NO' => OrderSlipDetail::getNewLineNumber($request->osnumber),
                    'SEQUENCE' => OrderSlipDetail::getNewProductSequence($request->osnumber, $request->product_id),
                    'OSDATE' => $orderSlip->OSDATE,
                    'ENCODEDDATE' => now(),
                    'STATUS' => 'X',
                    'DISPLAYMONITOR' => 2,
                    'LOCATIONID' => $product->PrinterLocation ?? 1,
                    'OSTYPE' => $orderSlip->OSTYPE ?? 1,
                    'IS_GROUP_MEAL' => $product->isGroupMeal ?? 0,
                    'GROUP_SERVING' => $product->Serving ?? 1
                ];

                $updatedItem = OrderSlipDetail::create($payload);
            }

            // Update order slip totals
            $this->updateOrderSlipTotals($orderSlip);

            DB::commit();

            return [
                'success' => true,
                'message' => 'Item added to cart successfully',
                'data' => [
                    'item_id' => $updatedItem->ORDERSLIPDETAILID,
                    'product_id' => $updatedItem->PRODUCT_ID,
                    'quantity' => $updatedItem->QUANTITY,
                    'amount' => $updatedItem->AMOUNT
                ]
            ];
        } catch (Exception $e) {
            DB::rollBack();
            return [
                'success' => false,
                'message' => 'Failed to add item to cart',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Update item quantity in cart
     */
    public function updateItemQuantity(Request $request)
    {
        try {
            DB::beginTransaction();

            $item = OrderSlipDetail::where('OSNUMBER', $request->osnumber)
                ->where('PRODUCT_ID', $request->product_id)
                ->where('STATUS', '!=', 'V')
                ->first();

            if (!$item) {
                DB::rollBack();
                return [
                    'success' => false,
                    'message' => 'Cart item not found'
                ];
            }

            $newQuantity = $request->quantity;
            
            if ($newQuantity <= 0) {
                // Remove item if quantity is 0 or negative
                $item->update(['STATUS' => 'V']); // Mark as voided
            } else {
                $newAmount = $newQuantity * $item->RETAILPRICE;
                $item->update([
                    'QUANTITY' => $newQuantity,
                    'REQUESTEDQTY' => $newQuantity,
                    'AMOUNT' => $newAmount,
                    'NETAMOUNT' => $newAmount
                ]);
            }

            // Update order slip totals
            $orderSlip = OrderSlipHeader::where('OSNUMBER', $request->osnumber)->first();
            $this->updateOrderSlipTotals($orderSlip);

            DB::commit();

            return [
                'success' => true,
                'message' => 'Cart item updated successfully',
                'data' => [
                    'item_id' => $item->ORDERSLIPDETAILID,
                    'product_id' => $item->PRODUCT_ID,
                    'quantity' => $newQuantity <= 0 ? 0 : $item->QUANTITY,
                    'amount' => $newQuantity <= 0 ? 0 : $item->AMOUNT
                ]
            ];
        } catch (Exception $e) {
            DB::rollBack();
            return [
                'success' => false,
                'message' => 'Failed to update cart item',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Remove item from cart
     */
    public function removeItem(Request $request)
    {
        try {
            DB::beginTransaction();

            $item = OrderSlipDetail::where('OSNUMBER', $request->osnumber)
                ->where('PRODUCT_ID', $request->product_id)
                ->where('STATUS', '!=', 'V')
                ->first();

            if (!$item) {
                DB::rollBack();
                return [
                    'success' => false,
                    'message' => 'Cart item not found'
                ];
            }

            // Mark item as voided instead of deleting
            $item->update(['STATUS' => 'V']);

            // Update order slip totals
            $orderSlip = OrderSlipHeader::where('OSNUMBER', $request->osnumber)->first();
            $this->updateOrderSlipTotals($orderSlip);

            DB::commit();

            return [
                'success' => true,
                'message' => 'Item removed from cart successfully'
            ];
        } catch (Exception $e) {
            DB::rollBack();
            return [
                'success' => false,
                'message' => 'Failed to remove item from cart',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Clear entire cart
     */
    public function clearCart(Request $request)
    {
        try {
            DB::beginTransaction();

            OrderSlipDetail::where('OSNUMBER', $request->osnumber)
                ->where('STATUS', '!=', 'V')
                ->update(['STATUS' => 'V']);

            // Update order slip totals
            $orderSlip = OrderSlipHeader::where('OSNUMBER', $request->osnumber)->first();
            $this->updateOrderSlipTotals($orderSlip);

            DB::commit();

            return [
                'success' => true,
                'message' => 'Cart cleared successfully'
            ];
        } catch (Exception $e) {
            DB::rollBack();
            return [
                'success' => false,
                'message' => 'Failed to clear cart',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get cart contents
     */
    public function getCart($osnumber)
    {
        return $this->cartInformationService->handle($osnumber);
    }

    /**
     * Update order slip totals based on current items
     */
    private function updateOrderSlipTotals(OrderSlipHeader $orderSlip)
    {
        $items = OrderSlipDetail::where('OSNUMBER', $orderSlip->OSNUMBER)
            ->where('STATUS', '!=', 'V')
            ->get();

        $totalAmount = $items->sum('AMOUNT');
        $netAmount = $items->sum('NETAMOUNT');

        // Calculate service charge if applicable
        $serviceChargeAmount = 0;
        if ($orderSlip->SERVICE_CHARGE_PERCENTAGE > 0) {
            $serviceChargeAmount = $totalAmount * ($orderSlip->SERVICE_CHARGE_PERCENTAGE / 100);
        }

        $finalTotal = $netAmount + $serviceChargeAmount;

        $orderSlip->update([
            'TOTALAMOUNT' => $finalTotal,
            'NETAMOUNT' => $netAmount,
            'SERVICE_CHARGE_AMT' => $serviceChargeAmount
        ]);
    }
}
