// resources/js/types/index.ts

export interface User {
  id: number
  cashier_number: string
  name: string
  role_id: number
  is_active: boolean
  role?: {
    id: number
    name: string
  }
}

export interface Product {
  id: number
  name: string
  description: string
  price: number
  category: string
  image?: string | null
  available: boolean
  quick_code: string
  serving: number
  is_group_meal: boolean
  printer_location: number
  tax_id?: number
  department_id: number
  delisted: boolean
}

export interface CartItem {
  id: number
  name: string
  price: number
  quantity: number
  total?: number
}

export interface OrderSlip {
  id: string
  orderslip_id?: number
  osnumber?: string
  status: 'attached' | 'detached' | 'active' | 'pending' | 'completed' | 'paid'
  created_at: string
  completed_at?: string | null
  total_amount?: number
  is_active?: boolean
  branch_id?: number
  outlet_id?: number
  device_id?: number
}

export interface Department {
  id: number
  name: string
  product_count?: number
}

export interface ApiResponse<T = any> {
  success: boolean
  message?: string
  data?: T
  error?: string
  pagination?: {
    current_page: number
    last_page: number
    per_page: number
    total: number
    from: number
    to: number
  }
}

export interface CartSummary {
  orderslip_code: string
  items_count: number
  total_amount: string
  net_amount: string
  service_charge_amount: string
  status: string
}

export interface OrderSlipDetail {
  item_id: number
  product_id: number
  quantity: number
  amount: number
  price: number
  name: string
}

export interface UserPermissions {
  can_create_orders: boolean
  can_modify_orders: boolean
  can_void_items: boolean
  can_apply_discounts: boolean
  can_view_reports: boolean
}

export interface SessionData {
  branch_id?: number
  outlet_id?: number
  device_id?: number
}

export interface LoadingState {
  [key: string]: boolean
}

export interface ErrorState {
  [key: string]: string | null
}

// Store state interfaces
export interface AuthState {
  user: User | null
  isAuthenticated: boolean
  permissions: UserPermissions | null
  sessionData: SessionData
  loading: LoadingState
  errors: ErrorState
}

export interface ProductState {
  products: Product[]
  departments: Department[]
  featuredProducts: Product[]
  currentProduct: Product | null
  loading: LoadingState
  errors: ErrorState
  filters: {
    department_id?: number
    search?: string
    available_only?: boolean
  }
}

export interface OrderSlipState {
  orderSlips: OrderSlip[]
  activeOrderSlipId: string | null
  currentOrderSlip: OrderSlip | null
  loading: LoadingState
  errors: ErrorState
}

export interface CartState {
  carts: Record<string, CartItem[]>
  loading: LoadingState
  errors: ErrorState
}

// API request/response types
export interface LoginRequest {
  cashier_number: string
  password: string
}

export interface CreateOrderSlipRequest {
  branch_id: number
  outlet_id: number
  device_id: number
  user_id: number
  user_name: string
}

export interface AddToCartRequest {
  osnumber: string
  product_id: number
  quantity?: number
}

export interface UpdateCartItemRequest {
  osnumber: string
  product_id: number
  quantity: number
}

export interface ProductSearchRequest {
  department_id?: number
  search?: string
  available_only?: boolean
  per_page?: number
}
