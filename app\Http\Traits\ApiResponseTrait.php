<?php

namespace App\Http\Traits;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Resources\Json\ResourceCollection;

trait ApiResponseTrait
{
    /**
     * Return a successful response.
     *
     * @param  mixed  $data
     * @param  string  $message
     * @param  int  $statusCode
     * @return \Illuminate\Http\JsonResponse
     */
    protected function successResponse($data = null, string $message = 'Success', int $statusCode = 200): JsonResponse
    {
        $response = [
            'success' => true,
            'message' => $message,
        ];

        if ($data !== null) {
            $response['data'] = $data;
        }

        return response()->json($response, $statusCode);
    }

    /**
     * Return an error response.
     *
     * @param  string  $message
     * @param  int  $statusCode
     * @param  mixed  $errors
     * @param  string|null  $errorCode
     * @return \Illuminate\Http\JsonResponse
     */
    protected function errorResponse(
        string $message = 'Error occurred', 
        int $statusCode = 400, 
        $errors = null,
        ?string $errorCode = null
    ): JsonResponse {
        $response = [
            'success' => false,
            'message' => $message,
        ];

        if ($errors !== null) {
            $response['errors'] = $errors;
        }

        if ($errorCode !== null) {
            $response['error_code'] = $errorCode;
        }

        return response()->json($response, $statusCode);
    }

    /**
     * Return a validation error response.
     *
     * @param  array  $errors
     * @param  string  $message
     * @return \Illuminate\Http\JsonResponse
     */
    protected function validationErrorResponse(array $errors, string $message = 'Validation failed'): JsonResponse
    {
        return $this->errorResponse($message, 422, $errors, 'VALIDATION_ERROR');
    }

    /**
     * Return a not found response.
     *
     * @param  string  $message
     * @return \Illuminate\Http\JsonResponse
     */
    protected function notFoundResponse(string $message = 'Resource not found'): JsonResponse
    {
        return $this->errorResponse($message, 404, null, 'RESOURCE_NOT_FOUND');
    }

    /**
     * Return an unauthorized response.
     *
     * @param  string  $message
     * @return \Illuminate\Http\JsonResponse
     */
    protected function unauthorizedResponse(string $message = 'Unauthorized'): JsonResponse
    {
        return $this->errorResponse($message, 401, null, 'UNAUTHORIZED');
    }

    /**
     * Return a forbidden response.
     *
     * @param  string  $message
     * @return \Illuminate\Http\JsonResponse
     */
    protected function forbiddenResponse(string $message = 'Forbidden'): JsonResponse
    {
        return $this->errorResponse($message, 403, null, 'FORBIDDEN');
    }

    /**
     * Return a server error response.
     *
     * @param  string  $message
     * @return \Illuminate\Http\JsonResponse
     */
    protected function serverErrorResponse(string $message = 'Internal server error'): JsonResponse
    {
        return $this->errorResponse($message, 500, null, 'INTERNAL_ERROR');
    }

    /**
     * Return a created response.
     *
     * @param  mixed  $data
     * @param  string  $message
     * @return \Illuminate\Http\JsonResponse
     */
    protected function createdResponse($data = null, string $message = 'Resource created successfully'): JsonResponse
    {
        return $this->successResponse($data, $message, 201);
    }

    /**
     * Return an updated response.
     *
     * @param  mixed  $data
     * @param  string  $message
     * @return \Illuminate\Http\JsonResponse
     */
    protected function updatedResponse($data = null, string $message = 'Resource updated successfully'): JsonResponse
    {
        return $this->successResponse($data, $message, 200);
    }

    /**
     * Return a deleted response.
     *
     * @param  string  $message
     * @return \Illuminate\Http\JsonResponse
     */
    protected function deletedResponse(string $message = 'Resource deleted successfully'): JsonResponse
    {
        return $this->successResponse(null, $message, 200);
    }

    /**
     * Return a paginated response.
     *
     * @param  \Illuminate\Http\Resources\Json\ResourceCollection  $collection
     * @param  string  $message
     * @return \Illuminate\Http\JsonResponse
     */
    protected function paginatedResponse(ResourceCollection $collection, string $message = 'Success'): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => $message,
            'data' => $collection->items(),
            'pagination' => [
                'current_page' => $collection->currentPage(),
                'last_page' => $collection->lastPage(),
                'per_page' => $collection->perPage(),
                'total' => $collection->total(),
                'from' => $collection->firstItem(),
                'to' => $collection->lastItem(),
            ]
        ]);
    }

    /**
     * Return a response with metadata.
     *
     * @param  mixed  $data
     * @param  array  $meta
     * @param  string  $message
     * @param  int  $statusCode
     * @return \Illuminate\Http\JsonResponse
     */
    protected function responseWithMeta(
        $data = null, 
        array $meta = [], 
        string $message = 'Success', 
        int $statusCode = 200
    ): JsonResponse {
        $response = [
            'success' => true,
            'message' => $message,
        ];

        if ($data !== null) {
            $response['data'] = $data;
        }

        if (!empty($meta)) {
            $response['meta'] = $meta;
        }

        return response()->json($response, $statusCode);
    }

    /**
     * Return a response for bulk operations.
     *
     * @param  array  $results
     * @param  string  $message
     * @return \Illuminate\Http\JsonResponse
     */
    protected function bulkOperationResponse(array $results, string $message = 'Bulk operation completed'): JsonResponse
    {
        $successful = collect($results)->where('success', true)->count();
        $failed = collect($results)->where('success', false)->count();

        return $this->responseWithMeta(
            $results,
            [
                'total' => count($results),
                'successful' => $successful,
                'failed' => $failed
            ],
            $message
        );
    }

    /**
     * Return a response for async operations.
     *
     * @param  string  $jobId
     * @param  string  $message
     * @return \Illuminate\Http\JsonResponse
     */
    protected function asyncOperationResponse(string $jobId, string $message = 'Operation started'): JsonResponse
    {
        return $this->responseWithMeta(
            null,
            ['job_id' => $jobId],
            $message,
            202
        );
    }

    /**
     * Return a no content response.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    protected function noContentResponse(): JsonResponse
    {
        return response()->json(null, 204);
    }

    /**
     * Transform data using a resource class.
     *
     * @param  mixed  $data
     * @param  string  $resourceClass
     * @param  string  $message
     * @param  int  $statusCode
     * @return \Illuminate\Http\JsonResponse
     */
    protected function resourceResponse(
        $data, 
        string $resourceClass, 
        string $message = 'Success', 
        int $statusCode = 200
    ): JsonResponse {
        $resource = new $resourceClass($data);
        return $this->successResponse($resource, $message, $statusCode);
    }

    /**
     * Transform collection using a resource class.
     *
     * @param  mixed  $collection
     * @param  string  $resourceClass
     * @param  string  $message
     * @param  int  $statusCode
     * @return \Illuminate\Http\JsonResponse
     */
    protected function collectionResponse(
        $collection, 
        string $resourceClass, 
        string $message = 'Success', 
        int $statusCode = 200
    ): JsonResponse {
        $resource = $resourceClass::collection($collection);
        return $this->successResponse($resource, $message, $statusCode);
    }
}
