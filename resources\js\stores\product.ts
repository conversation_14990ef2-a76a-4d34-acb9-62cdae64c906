// resources/js/stores/product.ts
import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'
import axios from 'axios'
import type { 
  Product, 
  Department, 
  LoadingState, 
  ErrorState, 
  ApiResponse,
  ProductSearchRequest 
} from '../types'

export const useProductStore = defineStore('product', () => {
  // State
  const products = ref<Product[]>([])
  const departments = ref<Department[]>([])
  const featuredProducts = ref<Product[]>([])
  const currentProduct = ref<Product | null>(null)
  const loading = ref<LoadingState>({})
  const errors = ref<ErrorState>({})
  const filters = ref<ProductSearchRequest>({})

  // Computed
  const availableProducts = computed(() => products.value.filter(p => p.available))
  const productsByDepartment = computed(() => {
    const grouped: Record<number, Product[]> = {}
    products.value.forEach(product => {
      if (!grouped[product.department_id]) {
        grouped[product.department_id] = []
      }
      grouped[product.department_id].push(product)
    })
    return grouped
  })
  const filteredProducts = computed(() => {
    let filtered = products.value

    if (filters.value.department_id) {
      filtered = filtered.filter(p => p.department_id === filters.value.department_id)
    }

    if (filters.value.search) {
      const search = filters.value.search.toLowerCase()
      filtered = filtered.filter(p => 
        p.name.toLowerCase().includes(search) ||
        p.description.toLowerCase().includes(search) ||
        p.quick_code.toLowerCase().includes(search)
      )
    }

    if (filters.value.available_only) {
      filtered = filtered.filter(p => p.available)
    }

    return filtered
  })

  // Actions
  async function fetchProducts(searchParams?: ProductSearchRequest): Promise<void> {
    setLoading('fetchProducts', true)
    clearError('fetchProducts')

    try {
      const params = { ...filters.value, ...searchParams }
      const response = await axios.get<ApiResponse<Product[]>>('/api/products', { params })
      
      if (response.data.success && response.data.data) {
        products.value = response.data.data.map(product => ({
          id: product.product_id || product.id,
          name: product.product_description || product.name,
          description: product.product_description || product.description,
          price: product.price,
          category: product.department_id?.toString() || 'Uncategorized',
          image: product.img_path && product.img_path !== '/assets/default-product.png' ? product.img_path : null,
          available: !product.delisted,
          quick_code: product.quick_code,
          serving: product.serving || 1,
          is_group_meal: product.is_group_meal || false,
          printer_location: product.printer_location || 1,
          tax_id: product.tax_id,
          department_id: product.department_id,
          delisted: product.delisted || false
        }))
      } else {
        products.value = []
        setError('fetchProducts', response.data.message || 'Failed to fetch products')
      }
    } catch (error: any) {
      console.error('fetchProducts error:', error)
      products.value = []
      setError('fetchProducts', error.response?.data?.message || 'Failed to fetch products')
    } finally {
      setLoading('fetchProducts', false)
    }
  }

  async function fetchProduct(productId: number): Promise<Product | null> {
    setLoading('fetchProduct', true)
    clearError('fetchProduct')

    try {
      const response = await axios.get<ApiResponse<Product>>(`/api/products/${productId}`)
      
      if (response.data.success && response.data.data) {
        const product = response.data.data
        currentProduct.value = {
          id: product.product_id || product.id,
          name: product.product_description || product.name,
          description: product.product_description || product.description,
          price: product.price,
          category: product.department_id?.toString() || 'Uncategorized',
          image: product.img_path && product.img_path !== '/assets/default-product.png' ? product.img_path : null,
          available: !product.delisted,
          quick_code: product.quick_code,
          serving: product.serving || 1,
          is_group_meal: product.is_group_meal || false,
          printer_location: product.printer_location || 1,
          tax_id: product.tax_id,
          department_id: product.department_id,
          delisted: product.delisted || false
        }
        return currentProduct.value
      } else {
        setError('fetchProduct', response.data.message || 'Product not found')
        return null
      }
    } catch (error: any) {
      setError('fetchProduct', error.response?.data?.message || 'Failed to fetch product')
      return null
    } finally {
      setLoading('fetchProduct', false)
    }
  }

  async function fetchDepartments(): Promise<void> {
    setLoading('fetchDepartments', true)
    clearError('fetchDepartments')

    try {
      const response = await axios.get<ApiResponse<Department[]>>('/api/products/departments')
      
      if (response.data.success && response.data.data) {
        departments.value = response.data.data
      } else {
        departments.value = []
        setError('fetchDepartments', response.data.message || 'Failed to fetch departments')
      }
    } catch (error: any) {
      departments.value = []
      setError('fetchDepartments', error.response?.data?.message || 'Failed to fetch departments')
    } finally {
      setLoading('fetchDepartments', false)
    }
  }

  async function fetchFeaturedProducts(limit = 10): Promise<void> {
    setLoading('fetchFeatured', true)
    clearError('fetchFeatured')

    try {
      const response = await axios.get<ApiResponse<Product[]>>('/api/products/featured', {
        params: { limit }
      })
      
      if (response.data.success && response.data.data) {
        featuredProducts.value = response.data.data.map(product => ({
          id: product.product_id || product.id,
          name: product.product_description || product.name,
          description: product.product_description || product.description,
          price: product.price,
          category: product.department_id?.toString() || 'Uncategorized',
          image: product.img_path && product.img_path !== '/assets/default-product.png' ? product.img_path : null,
          available: !product.delisted,
          quick_code: product.quick_code,
          serving: product.serving || 1,
          is_group_meal: product.is_group_meal || false,
          printer_location: product.printer_location || 1,
          tax_id: product.tax_id,
          department_id: product.department_id,
          delisted: product.delisted || false
        }))
      } else {
        featuredProducts.value = []
      }
    } catch (error: any) {
      featuredProducts.value = []
      setError('fetchFeatured', error.response?.data?.message || 'Failed to fetch featured products')
    } finally {
      setLoading('fetchFeatured', false)
    }
  }

  async function searchByQuickCode(quickCode: string): Promise<Product | null> {
    setLoading('searchQuickCode', true)
    clearError('searchQuickCode')

    try {
      const response = await axios.post<ApiResponse<Product>>('/api/products/search/quick-code', {
        quick_code: quickCode
      })
      
      if (response.data.success && response.data.data) {
        const product = response.data.data
        return {
          id: product.product_id || product.id,
          name: product.product_description || product.name,
          description: product.product_description || product.description,
          price: product.price,
          category: product.department_id?.toString() || 'Uncategorized',
          image: product.img_path && product.img_path !== '/assets/default-product.png' ? product.img_path : null,
          available: !product.delisted,
          quick_code: product.quick_code,
          serving: product.serving || 1,
          is_group_meal: product.is_group_meal || false,
          printer_location: product.printer_location || 1,
          tax_id: product.tax_id,
          department_id: product.department_id,
          delisted: product.delisted || false
        }
      } else {
        setError('searchQuickCode', response.data.message || 'Product not found')
        return null
      }
    } catch (error: any) {
      setError('searchQuickCode', error.response?.data?.message || 'Failed to search product')
      return null
    } finally {
      setLoading('searchQuickCode', false)
    }
  }

  async function checkAvailability(productId: number): Promise<boolean> {
    setLoading('checkAvailability', true)
    clearError('checkAvailability')

    try {
      const response = await axios.get<ApiResponse<{ available: boolean }>>(`/api/products/${productId}/availability`)
      
      if (response.data.success && response.data.data) {
        return response.data.data.available
      }
      return false
    } catch (error: any) {
      setError('checkAvailability', error.response?.data?.message || 'Failed to check availability')
      return false
    } finally {
      setLoading('checkAvailability', false)
    }
  }

  async function getRecommendations(currentProductIds: number[] = [], limit = 5): Promise<Product[]> {
    setLoading('getRecommendations', true)
    clearError('getRecommendations')

    try {
      const response = await axios.post<ApiResponse<Product[]>>('/api/products/recommendations', {
        current_products: currentProductIds,
        limit
      })
      
      if (response.data.success && response.data.data) {
        return response.data.data.map(product => ({
          id: product.product_id || product.id,
          name: product.product_description || product.name,
          description: product.product_description || product.description,
          price: product.price,
          category: product.department_id?.toString() || 'Uncategorized',
          image: product.img_path && product.img_path !== '/assets/default-product.png' ? product.img_path : null,
          available: !product.delisted,
          quick_code: product.quick_code,
          serving: product.serving || 1,
          is_group_meal: product.is_group_meal || false,
          printer_location: product.printer_location || 1,
          tax_id: product.tax_id,
          department_id: product.department_id,
          delisted: product.delisted || false
        }))
      }
      return []
    } catch (error: any) {
      setError('getRecommendations', error.response?.data?.message || 'Failed to get recommendations')
      return []
    } finally {
      setLoading('getRecommendations', false)
    }
  }

  // Filter actions
  function setFilters(newFilters: Partial<ProductSearchRequest>): void {
    filters.value = { ...filters.value, ...newFilters }
  }

  function clearFilters(): void {
    filters.value = {}
  }

  function setDepartmentFilter(departmentId: number | null): void {
    filters.value.department_id = departmentId || undefined
  }

  function setSearchFilter(search: string): void {
    filters.value.search = search || undefined
  }

  function setAvailabilityFilter(availableOnly: boolean): void {
    filters.value.available_only = availableOnly
  }

  // Utility functions
  function setLoading(key: string, value: boolean): void {
    loading.value[key] = value
  }

  function setError(key: string, message: string): void {
    errors.value[key] = message
  }

  function clearError(key: string): void {
    errors.value[key] = null
  }

  function isLoading(key?: string): boolean {
    if (key) return loading.value[key] || false
    return Object.values(loading.value).some(Boolean)
  }

  function getError(key: string): string | null {
    return errors.value[key] || null
  }

  function getProductById(id: number): Product | undefined {
    return products.value.find(p => p.id === id)
  }

  function getProductsByDepartmentId(departmentId: number): Product[] {
    return products.value.filter(p => p.department_id === departmentId)
  }

  return {
    // State
    products,
    departments,
    featuredProducts,
    currentProduct,
    loading,
    errors,
    filters,
    
    // Computed
    availableProducts,
    productsByDepartment,
    filteredProducts,
    
    // Actions
    fetchProducts,
    fetchProduct,
    fetchDepartments,
    fetchFeaturedProducts,
    searchByQuickCode,
    checkAvailability,
    getRecommendations,
    
    // Filter actions
    setFilters,
    clearFilters,
    setDepartmentFilter,
    setSearchFilter,
    setAvailabilityFilter,
    
    // Utilities
    setLoading,
    setError,
    clearError,
    isLoading,
    getError,
    getProductById,
    getProductsByDepartmentId
  }
})
