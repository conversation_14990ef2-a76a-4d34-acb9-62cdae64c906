import { ref, watch, onMounted, onUnmounted } from 'vue'
import { useCartStore } from '../stores/cart'
import { useOrderSlipStore } from '../stores/orderSlip'
import { useAuthStore } from '../stores/auth'
import type { CartItem, OrderSlip } from '../types'

/**
 * Composable for synchronizing state between frontend and backend
 */
export function useStateSynchronization() {
  const cartStore = useCartStore()
  const orderSlipStore = useOrderSlipStore()
  const authStore = useAuthStore()
  
  const syncInterval = ref<NodeJS.Timeout | null>(null)
  const lastSyncTime = ref<Date | null>(null)
  const syncInProgress = ref(false)
  const syncErrors = ref<string[]>([])

  // Configuration
  const SYNC_INTERVAL_MS = 30000 // 30 seconds
  const STORAGE_KEYS = {
    CART: 'restaurant_cart',
    ORDER_SLIP: 'restaurant_active_orderslip',
    LAST_SYNC: 'restaurant_last_sync'
  }

  /**
   * Save state to localStorage
   */
  const saveToLocalStorage = () => {
    try {
      // Save cart state
      if (orderSlipStore.hasActiveSlip) {
        const cartData = {
          orderSlipId: orderSlipStore.activeOrderSlipId,
          items: cartStore.activeItems,
          lastUpdated: new Date().toISOString()
        }
        localStorage.setItem(STORAGE_KEYS.CART, JSON.stringify(cartData))
      }

      // Save active order slip
      if (orderSlipStore.activeSlip) {
        localStorage.setItem(STORAGE_KEYS.ORDER_SLIP, JSON.stringify({
          slip: orderSlipStore.activeSlip,
          lastUpdated: new Date().toISOString()
        }))
      }

      // Save last sync time
      localStorage.setItem(STORAGE_KEYS.LAST_SYNC, new Date().toISOString())
    } catch (error) {
      console.error('Failed to save to localStorage:', error)
    }
  }

  /**
   * Load state from localStorage
   */
  const loadFromLocalStorage = () => {
    try {
      // Load cart state
      const cartData = localStorage.getItem(STORAGE_KEYS.CART)
      if (cartData) {
        const parsed = JSON.parse(cartData)
        // Restore cart items if we have an active order slip
        if (parsed.orderSlipId && orderSlipStore.activeOrderSlipId === parsed.orderSlipId) {
          // cartStore.restoreItems(parsed.items)
        }
      }

      // Load active order slip
      const orderSlipData = localStorage.getItem(STORAGE_KEYS.ORDER_SLIP)
      if (orderSlipData) {
        const parsed = JSON.parse(orderSlipData)
        // Restore active order slip
        // orderSlipStore.restoreActiveSlip(parsed.slip)
      }

      // Load last sync time
      const lastSync = localStorage.getItem(STORAGE_KEYS.LAST_SYNC)
      if (lastSync) {
        lastSyncTime.value = new Date(lastSync)
      }
    } catch (error) {
      console.error('Failed to load from localStorage:', error)
    }
  }

  /**
   * Sync cart with backend
   */
  const syncCartWithBackend = async (): Promise<boolean> => {
    if (!orderSlipStore.hasActiveSlip || !orderSlipStore.activeSlip?.osnumber) {
      return false
    }

    try {
      const response = await fetch(`/api/cart/${orderSlipStore.activeSlip.osnumber}`)
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      if (data.success && data.data) {
        // Update local cart with backend data
        // cartStore.syncFromBackend(data.data)
        return true
      }
      return false
    } catch (error) {
      console.error('Failed to sync cart with backend:', error)
      syncErrors.value.push(`Cart sync failed: ${error.message}`)
      return false
    }
  }

  /**
   * Sync order slip with backend
   */
  const syncOrderSlipWithBackend = async (): Promise<boolean> => {
    if (!authStore.user?.id) {
      return false
    }

    try {
      const response = await fetch('/api/orderslips/active')
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      if (data.success) {
        if (data.data) {
          // Update active order slip
          // orderSlipStore.syncActiveSlip(data.data)
        } else {
          // No active order slip on backend
          // orderSlipStore.clearActiveSlip()
        }
        return true
      }
      return false
    } catch (error) {
      console.error('Failed to sync order slip with backend:', error)
      syncErrors.value.push(`Order slip sync failed: ${error.message}`)
      return false
    }
  }

  /**
   * Full synchronization with backend
   */
  const performFullSync = async (): Promise<void> => {
    if (syncInProgress.value) {
      return
    }

    syncInProgress.value = true
    syncErrors.value = []

    try {
      // Sync order slip first
      const orderSlipSynced = await syncOrderSlipWithBackend()
      
      // Then sync cart if we have an active order slip
      if (orderSlipSynced && orderSlipStore.hasActiveSlip) {
        await syncCartWithBackend()
      }

      lastSyncTime.value = new Date()
      saveToLocalStorage()
    } catch (error) {
      console.error('Full sync failed:', error)
      syncErrors.value.push(`Full sync failed: ${error.message}`)
    } finally {
      syncInProgress.value = false
    }
  }

  /**
   * Start automatic synchronization
   */
  const startAutoSync = () => {
    if (syncInterval.value) {
      return // Already started
    }

    // Perform initial sync
    performFullSync()

    // Set up periodic sync
    syncInterval.value = setInterval(() => {
      performFullSync()
    }, SYNC_INTERVAL_MS)
  }

  /**
   * Stop automatic synchronization
   */
  const stopAutoSync = () => {
    if (syncInterval.value) {
      clearInterval(syncInterval.value)
      syncInterval.value = null
    }
  }

  /**
   * Handle online/offline events
   */
  const handleOnline = () => {
    console.log('Connection restored, performing sync...')
    performFullSync()
  }

  const handleOffline = () => {
    console.log('Connection lost, saving state locally...')
    saveToLocalStorage()
  }

  /**
   * Watch for cart changes and save to localStorage
   */
  const watchCartChanges = () => {
    watch(
      () => cartStore.activeItems,
      () => {
        saveToLocalStorage()
      },
      { deep: true }
    )
  }

  /**
   * Watch for order slip changes and save to localStorage
   */
  const watchOrderSlipChanges = () => {
    watch(
      () => orderSlipStore.activeSlip,
      () => {
        saveToLocalStorage()
      },
      { deep: true }
    )
  }

  /**
   * Initialize synchronization
   */
  const initializeSync = () => {
    // Load initial state from localStorage
    loadFromLocalStorage()

    // Set up watchers
    watchCartChanges()
    watchOrderSlipChanges()

    // Set up online/offline handlers
    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    // Start auto sync if user is authenticated
    if (authStore.isAuthenticated) {
      startAutoSync()
    }
  }

  /**
   * Cleanup synchronization
   */
  const cleanupSync = () => {
    stopAutoSync()
    window.removeEventListener('online', handleOnline)
    window.removeEventListener('offline', handleOffline)
  }

  // Lifecycle hooks
  onMounted(() => {
    initializeSync()
  })

  onUnmounted(() => {
    cleanupSync()
  })

  return {
    // State
    syncInProgress: readonly(syncInProgress),
    lastSyncTime: readonly(lastSyncTime),
    syncErrors: readonly(syncErrors),
    
    // Methods
    performFullSync,
    syncCartWithBackend,
    syncOrderSlipWithBackend,
    startAutoSync,
    stopAutoSync,
    saveToLocalStorage,
    loadFromLocalStorage,
    initializeSync,
    cleanupSync
  }
}

/**
 * Composable for handling network connectivity
 */
export function useNetworkStatus() {
  const isOnline = ref(navigator.onLine)
  const connectionType = ref<string>('unknown')

  const updateOnlineStatus = () => {
    isOnline.value = navigator.onLine
  }

  const updateConnectionType = () => {
    // @ts-ignore - navigator.connection is experimental
    const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection
    if (connection) {
      connectionType.value = connection.effectiveType || 'unknown'
    }
  }

  onMounted(() => {
    window.addEventListener('online', updateOnlineStatus)
    window.addEventListener('offline', updateOnlineStatus)
    
    // @ts-ignore
    if (navigator.connection) {
      // @ts-ignore
      navigator.connection.addEventListener('change', updateConnectionType)
      updateConnectionType()
    }
  })

  onUnmounted(() => {
    window.removeEventListener('online', updateOnlineStatus)
    window.removeEventListener('offline', updateOnlineStatus)
    
    // @ts-ignore
    if (navigator.connection) {
      // @ts-ignore
      navigator.connection.removeEventListener('change', updateConnectionType)
    }
  })

  return {
    isOnline: readonly(isOnline),
    connectionType: readonly(connectionType)
  }
}
