// resources/js/stores/product.js
import { defineStore } from 'pinia'
import { ref } from 'vue'
import axios from 'axios'

export const useProductStore = defineStore('product', () => {
  const products = ref([])
  const loading = ref(false)

  async function fetchProducts() {
    loading.value = true
    try {
      const response = await axios.get('/products')
      if (response.data?.success && Array.isArray(response.data.data)) {
        products.value = response.data.data.map(product => ({
          id: product.product_id,
          name: product.product_description,
          description: product.product_description,
          price: product.price,
          category: product.department_id || 'Uncategorized',
          image: product.img_path && product.img_path !== '/assets/default-product.png' ? product.img_path : null,
          available: !product.delisted,
          quick_code: product.quick_code,
          serving: product.serving,
          is_group_meal: product.is_group_meal,
          printer_location: product.printer_location,
        }))
      } else {
        products.value = []
      }
    } catch (e) {
      console.error('fetchProducts error:', e)
      products.value = []
    } finally {
      loading.value = false
    }
  }

  return {
    products,
    loading,
    fetchProducts,
  }
})