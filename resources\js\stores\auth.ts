// resources/js/stores/auth.ts
import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'
import axios from 'axios'
import type { 
  User, 
  UserPermissions, 
  SessionData, 
  LoadingState, 
  ErrorState, 
  ApiResponse,
  LoginRequest 
} from '../types'

const STORAGE_KEY = 'restaurant_auth_v1'

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref<User | null>(null)
  const isAuthenticated = ref(false)
  const permissions = ref<UserPermissions | null>(null)
  const sessionData = ref<SessionData>({})
  const loading = ref<LoadingState>({})
  const errors = ref<ErrorState>({})

  // Computed
  const userName = computed(() => user.value?.name || '')
  const userRole = computed(() => user.value?.role?.name || 'Unknown')
  const canCreateOrders = computed(() => permissions.value?.can_create_orders ?? false)
  const canModifyOrders = computed(() => permissions.value?.can_modify_orders ?? false)
  const canVoidItems = computed(() => permissions.value?.can_void_items ?? false)
  const canApplyDiscounts = computed(() => permissions.value?.can_apply_discounts ?? false)
  const canViewReports = computed(() => permissions.value?.can_view_reports ?? false)

  // Actions
  async function login(credentials: LoginRequest): Promise<boolean> {
    setLoading('login', true)
    clearError('login')

    try {
      const response = await axios.post<ApiResponse<{ user: User, session_id: string }>>('/api/auth/login', credentials)
      
      if (response.data.success && response.data.data) {
        user.value = response.data.data.user
        isAuthenticated.value = true
        await loadPermissions()
        saveToStorage()
        return true
      } else {
        setError('login', response.data.message || 'Login failed')
        return false
      }
    } catch (error: any) {
      const message = error.response?.data?.message || 'Login failed'
      setError('login', message)
      return false
    } finally {
      setLoading('login', false)
    }
  }

  async function logout(): Promise<void> {
    setLoading('logout', true)
    clearError('logout')

    try {
      await axios.post('/api/auth/logout')
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      // Clear state regardless of API call success
      user.value = null
      isAuthenticated.value = false
      permissions.value = null
      sessionData.value = {}
      clearStorage()
      setLoading('logout', false)
    }
  }

  async function getCurrentUser(): Promise<boolean> {
    setLoading('getCurrentUser', true)
    clearError('getCurrentUser')

    try {
      const response = await axios.get<ApiResponse<User>>('/api/auth/user')
      
      if (response.data.success && response.data.data) {
        user.value = response.data.data
        isAuthenticated.value = true
        return true
      } else {
        user.value = null
        isAuthenticated.value = false
        return false
      }
    } catch (error: any) {
      user.value = null
      isAuthenticated.value = false
      setError('getCurrentUser', error.response?.data?.message || 'Failed to get user')
      return false
    } finally {
      setLoading('getCurrentUser', false)
    }
  }

  async function checkAuth(): Promise<boolean> {
    setLoading('checkAuth', true)
    clearError('checkAuth')

    try {
      const response = await axios.get<ApiResponse<{ authenticated: boolean, user: User | null }>>('/api/auth/check')
      
      if (response.data.success && response.data.data) {
        isAuthenticated.value = response.data.data.authenticated
        user.value = response.data.data.user
        
        if (isAuthenticated.value && user.value) {
          await loadPermissions()
        }
        
        return isAuthenticated.value
      }
      
      return false
    } catch (error: any) {
      isAuthenticated.value = false
      user.value = null
      setError('checkAuth', error.response?.data?.message || 'Auth check failed')
      return false
    } finally {
      setLoading('checkAuth', false)
    }
  }

  async function updateSession(data: SessionData): Promise<boolean> {
    setLoading('updateSession', true)
    clearError('updateSession')

    try {
      const response = await axios.post<ApiResponse<{ user: User, session_data: SessionData }>>('/api/auth/session', data)
      
      if (response.data.success && response.data.data) {
        sessionData.value = { ...sessionData.value, ...response.data.data.session_data }
        saveToStorage()
        return true
      } else {
        setError('updateSession', response.data.message || 'Failed to update session')
        return false
      }
    } catch (error: any) {
      setError('updateSession', error.response?.data?.message || 'Failed to update session')
      return false
    } finally {
      setLoading('updateSession', false)
    }
  }

  async function loadPermissions(): Promise<void> {
    if (!isAuthenticated.value) return

    setLoading('loadPermissions', true)
    clearError('loadPermissions')

    try {
      const response = await axios.get<ApiResponse<{ permissions: UserPermissions }>>('/api/auth/permissions')
      
      if (response.data.success && response.data.data) {
        permissions.value = response.data.data.permissions
      }
    } catch (error: any) {
      setError('loadPermissions', error.response?.data?.message || 'Failed to load permissions')
    } finally {
      setLoading('loadPermissions', false)
    }
  }

  async function validateSession(): Promise<boolean> {
    setLoading('validateSession', true)
    clearError('validateSession')

    try {
      const response = await axios.get<ApiResponse<User>>('/api/auth/validate')
      
      if (response.data.success && response.data.data) {
        user.value = response.data.data
        isAuthenticated.value = true
        return true
      } else {
        user.value = null
        isAuthenticated.value = false
        return false
      }
    } catch (error: any) {
      user.value = null
      isAuthenticated.value = false
      setError('validateSession', error.response?.data?.message || 'Session validation failed')
      return false
    } finally {
      setLoading('validateSession', false)
    }
  }

  // Utility functions
  function setLoading(key: string, value: boolean): void {
    loading.value[key] = value
  }

  function setError(key: string, message: string): void {
    errors.value[key] = message
  }

  function clearError(key: string): void {
    errors.value[key] = null
  }

  function clearAllErrors(): void {
    errors.value = {}
  }

  function isLoading(key?: string): boolean {
    if (key) {
      return loading.value[key] || false
    }
    return Object.values(loading.value).some(Boolean)
  }

  function getError(key: string): string | null {
    return errors.value[key] || null
  }

  // Storage functions
  function saveToStorage(): void {
    try {
      const data = {
        user: user.value,
        isAuthenticated: isAuthenticated.value,
        permissions: permissions.value,
        sessionData: sessionData.value
      }
      localStorage.setItem(STORAGE_KEY, JSON.stringify(data))
    } catch (error) {
      console.error('Failed to save auth data to storage:', error)
    }
  }

  function loadFromStorage(): void {
    try {
      const stored = localStorage.getItem(STORAGE_KEY)
      if (stored) {
        const data = JSON.parse(stored)
        user.value = data.user || null
        isAuthenticated.value = data.isAuthenticated || false
        permissions.value = data.permissions || null
        sessionData.value = data.sessionData || {}
      }
    } catch (error) {
      console.error('Failed to load auth data from storage:', error)
      clearStorage()
    }
  }

  function clearStorage(): void {
    try {
      localStorage.removeItem(STORAGE_KEY)
    } catch (error) {
      console.error('Failed to clear auth storage:', error)
    }
  }

  // Watch for changes and save to storage
  watch([user, isAuthenticated, permissions, sessionData], saveToStorage, { deep: true })

  // Initialize from storage
  loadFromStorage()

  return {
    // State
    user,
    isAuthenticated,
    permissions,
    sessionData,
    loading,
    errors,
    
    // Computed
    userName,
    userRole,
    canCreateOrders,
    canModifyOrders,
    canVoidItems,
    canApplyDiscounts,
    canViewReports,
    
    // Actions
    login,
    logout,
    getCurrentUser,
    checkAuth,
    updateSession,
    loadPermissions,
    validateSession,
    
    // Utilities
    setLoading,
    setError,
    clearError,
    clearAllErrors,
    isLoading,
    getError,
    saveToStorage,
    loadFromStorage,
    clearStorage
  }
})
