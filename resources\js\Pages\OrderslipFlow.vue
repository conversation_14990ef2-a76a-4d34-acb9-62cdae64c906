<template>
  <div class="p-6 space-y-6">
    <div class="flex items-center justify-between">
      <h1 class="text-2xl font-bold">Pending Order Slips</h1>
      <Button label="New Transaction" icon="pi pi-plus" severity="success" @click="onNewTransaction" v-if="!hasActiveSlip"/>
    </div>

    <div class="space-y-2">
      <div v-for="slip in orderSlips" :key="slip.id" class="flex items-center justify-between p-3 border rounded">
        <div class="flex items-center gap-3">
          <span class="font-semibold">#{{ slip.id }}</span>
          <span class="text-xs px-2 py-1 rounded" :class="slip.status === 'attached' ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'">
            {{ slip.status }}
          </span>
          <span class="text-xs text-gray-500">{{ new Date(slip.createdAt).toLocaleString() }}</span>
        </div>
        <div class="flex items-center gap-2">
          <Button size="small" @click="attachSlip(slip.id)" :label="slip.status === 'attached' ? 'Active' : 'Attach'"/>
          <Button size="small" severity="secondary" outlined label="Remove" @click="removeSlip(slip.id)"/>
        </div>
      </div>
    </div>

    <div v-if="hasActiveSlip" class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <div class="lg:col-span-2">
        <Dashboard :user="user" />
      </div>
      <div class="lg:col-span-1">
        <Cart :user="user" />
      </div>
    </div>

    <div v-else class="text-center py-12 text-gray-600">
      No active order slip. Click "New Transaction" to start.
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import Button from 'primevue/button'
import { useOrderSlipStore } from '../stores/orderSlip'
import Dashboard from './Dashboard.vue'
import Cart from './Cart.vue'

const props = defineProps({
  user: { type: Object, required: true },
})

const orderSlipStore = useOrderSlipStore()
const orderSlips = computed(() => orderSlipStore.orderSlips)
const hasActiveSlip = computed(() => orderSlipStore.hasActiveSlip)

function onNewTransaction() { orderSlipStore.createNewSlip() }
function attachSlip(id) { orderSlipStore.attachSlip(id) }
function removeSlip(id) { orderSlipStore.removeSlip(id) }
</script>