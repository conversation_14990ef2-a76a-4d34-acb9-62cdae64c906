// resources/js/stores/orderSlip.ts
import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'
import axios from 'axios'
import type { 
  OrderSlip, 
  LoadingState, 
  ErrorState, 
  ApiResponse,
  CreateOrderSlipRequest 
} from '../types'
import { useAuthStore } from './auth'

const STORAGE_KEY = 'restaurant_order_slips_v1'

export const useOrderSlipStore = defineStore('orderSlip', () => {
  // State
  const orderSlips = ref<OrderSlip[]>([])
  const activeOrderSlipId = ref<string | null>(null)
  const currentOrderSlip = ref<OrderSlip | null>(null)
  const loading = ref<LoadingState>({})
  const errors = ref<ErrorState>({})

  // Computed
  const hasActiveSlip = computed(() => !!activeOrderSlipId.value)
  const activeSlip = computed(() => orderSlips.value.find(s => s.id === activeOrderSlipId.value) || null)
  const pendingSlips = computed(() => orderSlips.value.filter(s => s.status === 'pending'))
  const completedSlips = computed(() => orderSlips.value.filter(s => s.status === 'completed'))

  // Actions
  async function createNewSlip(request?: CreateOrderSlipRequest): Promise<string | null> {
    setLoading('createSlip', true)
    clearError('createSlip')

    try {
      if (request) {
        // Create via API
        const response = await axios.post<ApiResponse<{ orderslip_id: number, osnumber: string }>>('/api/orderslips', request)
        
        if (response.data.success && response.data.data) {
          const newSlip: OrderSlip = {
            id: response.data.data.osnumber,
            orderslip_id: response.data.data.orderslip_id,
            osnumber: response.data.data.osnumber,
            status: 'active',
            created_at: new Date().toISOString(),
            is_active: true
          }
          
          orderSlips.value.unshift(newSlip)
          activeOrderSlipId.value = newSlip.id
          currentOrderSlip.value = newSlip
          return newSlip.id
        } else {
          setError('createSlip', response.data.message || 'Failed to create order slip')
          return null
        }
      } else {
        // Create locally (fallback)
        const id = Date.now().toString()
        const slip: OrderSlip = { 
          id, 
          status: 'attached', 
          created_at: new Date().toISOString() 
        }
        orderSlips.value.unshift(slip)
        activeOrderSlipId.value = id
        currentOrderSlip.value = slip
        return id
      }
    } catch (error: any) {
      setError('createSlip', error.response?.data?.message || 'Failed to create order slip')
      return null
    } finally {
      setLoading('createSlip', false)
    }
  }

  async function loadUserOrderSlips(userId?: number, branchId?: number): Promise<void> {
    setLoading('loadSlips', true)
    clearError('loadSlips')

    try {
      const params: any = {}
      if (userId) params.user_id = userId
      if (branchId) params.branch_id = branchId

      const response = await axios.get<ApiResponse<OrderSlip[]>>('/api/orderslips/user', { params })
      
      if (response.data.success && response.data.data) {
        orderSlips.value = response.data.data.map(slip => ({
          ...slip,
          id: slip.osnumber || slip.id
        }))
        
        // Set active slip if one exists
        const activeSlip = orderSlips.value.find(s => s.is_active)
        if (activeSlip) {
          activeOrderSlipId.value = activeSlip.id
          currentOrderSlip.value = activeSlip
        }
      }
    } catch (error: any) {
      setError('loadSlips', error.response?.data?.message || 'Failed to load order slips')
    } finally {
      setLoading('loadSlips', false)
    }
  }

  async function getUserActiveOrderSlip(userId?: number, branchId?: number): Promise<OrderSlip | null> {
    setLoading('getActiveSlip', true)
    clearError('getActiveSlip')

    try {
      const params: any = {}
      if (userId) params.user_id = userId
      if (branchId) params.branch_id = branchId

      const response = await axios.get<ApiResponse<OrderSlip | null>>('/api/orderslips/active', { params })
      
      if (response.data.success) {
        const activeSlip = response.data.data
        if (activeSlip) {
          currentOrderSlip.value = { ...activeSlip, id: activeSlip.osnumber || activeSlip.id }
          activeOrderSlipId.value = currentOrderSlip.value.id
          
          // Update or add to orderSlips array
          const existingIndex = orderSlips.value.findIndex(s => s.id === currentOrderSlip.value!.id)
          if (existingIndex >= 0) {
            orderSlips.value[existingIndex] = currentOrderSlip.value
          } else {
            orderSlips.value.unshift(currentOrderSlip.value)
          }
        } else {
          currentOrderSlip.value = null
          activeOrderSlipId.value = null
        }
        return currentOrderSlip.value
      }
      return null
    } catch (error: any) {
      setError('getActiveSlip', error.response?.data?.message || 'Failed to get active order slip')
      return null
    } finally {
      setLoading('getActiveSlip', false)
    }
  }

  async function attachSlip(id: string): Promise<boolean> {
    setLoading('attachSlip', true)
    clearError('attachSlip')

    try {
      const slip = orderSlips.value.find(s => s.id === id)
      if (!slip) {
        setError('attachSlip', 'Order slip not found')
        return false
      }

      const authStore = useAuthStore()
      const response = await axios.post<ApiResponse>('/api/orderslips/attach', {
        osnumber: slip.osnumber || id,
        user_id: authStore.user?.id
      })

      if (response.data.success) {
        // Update local state
        orderSlips.value.forEach(s => {
          if (s.id === activeOrderSlipId.value) s.status = 'detached'
        })
        slip.status = 'attached'
        activeOrderSlipId.value = slip.id
        currentOrderSlip.value = slip
        return true
      } else {
        setError('attachSlip', response.data.message || 'Failed to attach order slip')
        return false
      }
    } catch (error: any) {
      setError('attachSlip', error.response?.data?.message || 'Failed to attach order slip')
      return false
    } finally {
      setLoading('attachSlip', false)
    }
  }

  async function detachActiveSlip(): Promise<boolean> {
    if (!activeOrderSlipId.value) return true

    setLoading('detachSlip', true)
    clearError('detachSlip')

    try {
      const authStore = useAuthStore()
      const response = await axios.post<ApiResponse>('/api/orderslips/detach', {
        osnumber: activeSlip.value?.osnumber || activeOrderSlipId.value,
        user_id: authStore.user?.id
      })

      if (response.data.success) {
        const slip = orderSlips.value.find(s => s.id === activeOrderSlipId.value)
        if (slip) slip.status = 'detached'
        activeOrderSlipId.value = null
        currentOrderSlip.value = null
        return true
      } else {
        setError('detachSlip', response.data.message || 'Failed to detach order slip')
        return false
      }
    } catch (error: any) {
      setError('detachSlip', error.response?.data?.message || 'Failed to detach order slip')
      return false
    } finally {
      setLoading('detachSlip', false)
    }
  }

  function switchActive(id: string): void {
    if (id === activeOrderSlipId.value) return
    attachSlip(id)
  }

  function removeSlip(id: string): void {
    if (id === activeOrderSlipId.value) {
      activeOrderSlipId.value = null
      currentOrderSlip.value = null
    }
    orderSlips.value = orderSlips.value.filter(s => s.id !== id)
  }

  // Utility functions
  function setLoading(key: string, value: boolean): void {
    loading.value[key] = value
  }

  function setError(key: string, message: string): void {
    errors.value[key] = message
  }

  function clearError(key: string): void {
    errors.value[key] = null
  }

  function isLoading(key?: string): boolean {
    if (key) return loading.value[key] || false
    return Object.values(loading.value).some(Boolean)
  }

  function getError(key: string): string | null {
    return errors.value[key] || null
  }

  // Storage functions
  function saveToStorage(): void {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify({
        orderSlips: orderSlips.value,
        activeOrderSlipId: activeOrderSlipId.value,
      }))
    } catch (e) {
      console.error('OrderSlip save error:', e)
    }
  }

  function loadFromStorage(): void {
    try {
      const raw = localStorage.getItem(STORAGE_KEY)
      if (raw) {
        const data = JSON.parse(raw)
        orderSlips.value = data.orderSlips || []
        activeOrderSlipId.value = data.activeOrderSlipId || null
        currentOrderSlip.value = orderSlips.value.find(s => s.id === activeOrderSlipId.value) || null
      }
    } catch (e) {
      console.error('OrderSlip load error:', e)
    }
  }

  // Watch for changes and save to storage
  watch([orderSlips, activeOrderSlipId], saveToStorage, { deep: true })

  // Initialize from storage
  loadFromStorage()

  return {
    // State
    orderSlips,
    activeOrderSlipId,
    currentOrderSlip,
    loading,
    errors,
    
    // Computed
    hasActiveSlip,
    activeSlip,
    pendingSlips,
    completedSlips,
    
    // Actions
    createNewSlip,
    loadUserOrderSlips,
    getUserActiveOrderSlip,
    attachSlip,
    detachActiveSlip,
    switchActive,
    removeSlip,
    
    // Utilities
    setLoading,
    setError,
    clearError,
    isLoading,
    getError,
    saveToStorage,
    loadFromStorage
  }
})
