<?php

namespace App\Http\Controllers;

use App\Http\Services\ProductService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ProductController extends Controller
{
    public function __construct(
        protected ProductService $productService
    ) {}

    /**
     * Get all products with optional filtering
     */
    public function index(Request $request): JsonResponse
    {
        $result = $this->productService->getProducts($request);
        return response()->json($result);
    }

    /**
     * Get single product by ID
     */
    public function show($productId): JsonResponse
    {
        $result = $this->productService->getProduct($productId);
        return response()->json($result, $result['success'] ? 200 : 404);
    }

    /**
     * Get products by department/category
     */
    public function getByDepartment($departmentId): JsonResponse
    {
        $result = $this->productService->getProductsByDepartment($departmentId);
        return response()->json($result, $result['success'] ? 200 : 404);
    }

    /**
     * Search products by quick code
     */
    public function searchByQuickCode(Request $request): JsonResponse
    {
        $request->validate([
            'quick_code' => 'required|string'
        ]);

        $result = $this->productService->searchByQuickCode($request->quick_code);
        return response()->json($result, $result['success'] ? 200 : 404);
    }

    /**
     * Get featured/popular products
     */
    public function getFeatured(Request $request): JsonResponse
    {
        $limit = $request->get('limit', 10);
        $result = $this->productService->getFeaturedProducts($limit);
        return response()->json($result);
    }

    /**
     * Get all departments with product counts
     */
    public function getDepartments(): JsonResponse
    {
        $result = $this->productService->getDepartmentsWithProductCounts();
        return response()->json($result);
    }

    /**
     * Check product availability
     */
    public function checkAvailability($productId): JsonResponse
    {
        $result = $this->productService->checkAvailability($productId);
        return response()->json($result, $result['success'] ? 200 : 404);
    }

    /**
     * Get product recommendations
     */
    public function getRecommendations(Request $request): JsonResponse
    {
        $currentProductIds = $request->get('current_products', []);
        $limit = $request->get('limit', 5);

        $result = $this->productService->getRecommendations($currentProductIds, $limit);
        return response()->json($result);
    }
}
