<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Application Type
    |--------------------------------------------------------------------------
    |
    | This value determines the application type for the system.
    | Options: 'restaurant_ambulant', 'shell_ambulant'
    |
    */

    'app_type' => env('APP_TYPE', 'restaurant_ambulant'),

    /*
    |--------------------------------------------------------------------------
    | Service Charge Percentage
    |--------------------------------------------------------------------------
    |
    | The default service charge percentage for orders.
    |
    */

    'service_charge_percentage' => env('SERVICE_CHARGE_PERCENTAGE', 10),

    /*
    |--------------------------------------------------------------------------
    | Default Order Type
    |--------------------------------------------------------------------------
    |
    | The default order type for restaurant ambulant.
    | 1 = Dine In, 2 = Take Out
    |
    */

    'default_order_type' => env('DEFAULT_ORDER_TYPE', 1),

    /*
    |--------------------------------------------------------------------------
    | Default System IDs
    |--------------------------------------------------------------------------
    |
    | Default branch, outlet, and device IDs for order slip creation.
    |
    */

    'default_branch_id' => env('DEFAULT_BRANCH_ID', 1),
    'default_outlet_id' => env('DEFAULT_OUTLET_ID', 1),
    'default_device_id' => env('DEFAULT_DEVICE_ID', 1),

];