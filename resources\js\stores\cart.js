// resources/js/stores/cart.js
import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'
import { useOrderSlipStore } from './orderSlip'

const STORAGE_KEY = 'restaurant_carts_v1'

export const useCartStore = defineStore('cart', () => {
  const carts = ref({}) // slipId -> items[]

  function loadFromStorage() {
    try {
      const raw = localStorage.getItem(STORAGE_KEY)
      if (raw) {
        carts.value = JSON.parse(raw) || {}
      }
    } catch (e) {
      console.error('Cart load error:', e)
    }
  }

  function saveToStorage() {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(carts.value))
    } catch (e) {
      console.error('Cart save error:', e)
    }
  }

  function ensureCartForSlip(slipId) {
    if (!carts.value[slipId]) carts.value[slipId] = []
    return carts.value[slipId]
  }

  const activeItems = computed(() => {
    const orderSlipStore = useOrderSlipStore()
    const slipId = orderSlipStore.activeOrderSlipId
    if (!slipId) return []
    return ensureCartForSlip(slipId)
  })

  const itemCount = computed(() => activeItems.value.reduce((sum, i) => sum + i.quantity, 0))
  const total = computed(() => activeItems.value.reduce((sum, i) => sum + i.price * i.quantity, 0))

  function addItem(product, qty = 1) {
    const orderSlipStore = useOrderSlipStore()
    const slipId = orderSlipStore.activeOrderSlipId
    if (!slipId) return
    const items = ensureCartForSlip(slipId)
    const existing = items.find(i => i.id === product.id)
    if (existing) existing.quantity += qty
    else items.push({ id: product.id, name: product.name, price: product.price, quantity: qty })
  }

  function increase(productId) {
    const orderSlipStore = useOrderSlipStore()
    const slipId = orderSlipStore.activeOrderSlipId
    if (!slipId) return
    const items = ensureCartForSlip(slipId)
    const item = items.find(i => i.id === productId)
    if (item) item.quantity += 1
  }

  function decrease(productId) {
    const orderSlipStore = useOrderSlipStore()
    const slipId = orderSlipStore.activeOrderSlipId
    if (!slipId) return
    const items = ensureCartForSlip(slipId)
    const item = items.find(i => i.id === productId)
    if (!item) return
    if (item.quantity > 1) item.quantity -= 1
    else carts.value[slipId] = items.filter(i => i.id !== productId)
  }

  function clearActive() {
    const orderSlipStore = useOrderSlipStore()
    const slipId = orderSlipStore.activeOrderSlipId
    if (!slipId) return
    carts.value[slipId] = []
  }

  watch(carts, saveToStorage, { deep: true })
  loadFromStorage()

  return {
    carts,
    activeItems,
    itemCount,
    total,
    addItem,
    increase,
    decrease,
    clearActive,
  }
})