<?php

namespace App\Http\Controllers;

use App\Http\Services\OrderslipService;
use App\Http\Requests\CreateOrderSlipRequest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class OrderSlipController extends Controller
{
    public function __construct(
        protected OrderslipService $orderslipService
    ) {}

    /**
     * Display pending order slips page
     */
    public function pendingOrderslipsPage(): Response
    {
        return Inertia::render('PendingOrderslips', [
            'user' => Auth::user()
        ]);
    }

    /**
     * Get list of order slips
     */
    public function index(Request $request): JsonResponse
    {
        $result = $this->orderslipService->list($request);
        return response()->json($result);
    }

    /**
     * Create new order slip
     */
    public function store(CreateOrderSlipRequest $request): JsonResponse
    {
        $result = $this->orderslipService->create($request);

        return response()->json($result, $result['success'] ? 200 : 400);
    }

    /**
     * Get order slip details
     */
    public function show(string $orderslip_code): JsonResponse
    {
        $result = $this->orderslipService->show($orderslip_code);
        return response()->json($result);
    }

    /**
     * Get user's active order slip
     */
    public function getUserActiveOrderSlip(Request $request): JsonResponse
    {
        $userId = $request->user_id ?? Auth::id();
        $branchId = $request->branch_id;
        $outletId = $request->outlet_id;

        $result = $this->orderslipService->getUserActiveOrderSlip($userId, $branchId, $outletId);
        return response()->json($result);
    }

    /**
     * Get user's order slips
     */
    public function getUserOrderSlips(Request $request): JsonResponse
    {
        $userId = $request->user_id ?? Auth::id();
        $branchId = $request->branch_id;
        $limit = $request->limit ?? 10;

        $result = $this->orderslipService->getUserOrderSlips($userId, $branchId, $limit);
        return response()->json($result);
    }

    /**
     * Attach order slip to user
     */
    public function attachOrderSlip(Request $request): JsonResponse
    {
        $request->validate([
            'osnumber' => 'required|string',
            'user_id' => 'integer'
        ]);

        $userId = $request->user_id ?? Auth::id();
        $result = $this->orderslipService->attachOrderSlip($request->osnumber, $userId);

        return response()->json($result, $result['success'] ? 200 : 400);
    }

    /**
     * Detach order slip from user
     */
    public function detachOrderSlip(Request $request): JsonResponse
    {
        $request->validate([
            'osnumber' => 'required|string',
            'user_id' => 'integer'
        ]);

        $userId = $request->user_id ?? Auth::id();
        $result = $this->orderslipService->detachOrderSlip($request->osnumber, $userId);

        return response()->json($result, $result['success'] ? 200 : 400);
    }
}


