<?php

namespace App\Http\Controllers;

use App\Services\OrderslipService;
use Illuminate\Http\Request;


class OrderSlipController extends Controller
{
     public function __construct(OrderslipService $orderslipService)
    {
        $this->orderslipService = $orderslipService;
    }

    public function index (Request $request) {
        $items = $this->OrderslipService->list($request);

    }

    public function store(Request $request) {
        return $this->orderslipService->create($request);
    }

    public function orderSlipOverview(Request $request, $orderslip_code) {
        return $this->orderslipService->show($orderslip_code);
    }
}


