<?php

use App\Http\Controllers\AuthController;
use App\Http\Controllers\OrderSlipController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\CartController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Authentication routes
Route::prefix('auth')->group(function () {
    Route::post('/login', [AuthController::class, 'login']);
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::get('/check', [AuthController::class, 'checkAuth']);
    Route::get('/validate', [AuthController::class, 'validateSession']);

    Route::middleware('auth')->group(function () {
        Route::get('/user', [AuthController::class, 'getCurrentUser']);
        Route::post('/session', [AuthController::class, 'updateSession']);
        Route::get('/permissions', [AuthController::class, 'getPermissions']);
    });
});

// Product routes
Route::prefix('products')->group(function () {
    Route::get('/', [ProductController::class, 'index']);
    Route::get('/featured', [ProductController::class, 'getFeatured']);
    Route::get('/departments', [ProductController::class, 'getDepartments']);
    Route::get('/department/{departmentId}', [ProductController::class, 'getByDepartment']);
    Route::get('/{productId}', [ProductController::class, 'show']);
    Route::get('/{productId}/availability', [ProductController::class, 'checkAvailability']);
    Route::post('/search/quick-code', [ProductController::class, 'searchByQuickCode']);
    Route::post('/recommendations', [ProductController::class, 'getRecommendations']);
});

// Order slip routes
Route::middleware('auth')->prefix('orderslips')->group(function () {
    Route::get('/', [OrderSlipController::class, 'index']);
    Route::post('/', [OrderSlipController::class, 'store']);
    Route::get('/user', [OrderSlipController::class, 'getUserOrderSlips']);
    Route::get('/active', [OrderSlipController::class, 'getUserActiveOrderSlip']);
    Route::post('/attach', [OrderSlipController::class, 'attachOrderSlip']);
    Route::post('/detach', [OrderSlipController::class, 'detachOrderSlip']);
    Route::get('/{orderslip_code}', [OrderSlipController::class, 'show']);
});

// Cart routes
Route::middleware('auth')->prefix('cart')->group(function () {
    Route::get('/{orderslip_code}', [CartController::class, 'show']);
    Route::get('/{orderslip_code}/summary', [CartController::class, 'getSummary']);
    Route::post('/add', [CartController::class, 'addItem']);
    Route::post('/update', [CartController::class, 'updateItem']);
    Route::post('/remove', [CartController::class, 'removeItem']);
    Route::post('/clear', [CartController::class, 'clearCart']);
});

// Legacy route for compatibility
Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});
